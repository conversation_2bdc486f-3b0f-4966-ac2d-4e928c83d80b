#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update the reviewer response document with correct page and section references
from the compiled PDF manuscript.
"""

from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn

def create_updated_reviewer_response():
    """Create an updated reviewer response document with correct references."""
    
    # Create a new document
    doc = Document()
    
    # Add title
    title = doc.add_heading('Response to Reviewers', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add manuscript info
    doc.add_paragraph().add_run('Manuscript Title: ').bold = True
    doc.add_paragraph('Daily Prediction Model for Harmful Algal Blooms (HABs) using geospatial satellite data')
    
    doc.add_paragraph().add_run('Journal: ').bold = True
    doc.add_paragraph('MDPI Water')
    
    doc.add_paragraph().add_run('Date: ').bold = True
    doc.add_paragraph('July 28, 2025')
    
    doc.add_paragraph('---' * 30)
    
    # Add summary
    doc.add_heading('Summary of Changes', level=1)
    doc.add_paragraph(
        'We thank the reviewers for their constructive feedback. We have carefully addressed '
        'all comments and made significant improvements to the manuscript. Below is a detailed '
        'point-by-point response to each reviewer\'s comments, with corresponding changes made '
        'to the manuscript.'
    )
    
    # REVIEWER 1
    doc.add_heading('REVIEWER 1', level=1)
    
    doc.add_paragraph().add_run('Comment 1: ').bold = True
    doc.add_paragraph().add_run(
        'The only recommendation I have is to review the title: "… Model for Harmful Algae Blooms (HABs)…" '
        'I think the correct word is "Algal"'
    ).bold = True
    
    doc.add_paragraph().add_run('Response: ').bold = True
    doc.add_paragraph(
        'Thank you for this important correction. We have updated the title from "Harmful Algae Blooms" '
        'to "Harmful Algal Blooms" throughout the manuscript. This correction has been made in:'
    )
    
    changes_list = doc.add_paragraph(style='List Bullet')
    changes_list.add_run('Title (page 1)')
    
    changes_list = doc.add_paragraph(style='List Bullet')
    changes_list.add_run('Abstract (page 1)')
    
    changes_list = doc.add_paragraph(style='List Bullet')
    changes_list.add_run('Keywords (page 1)')
    
    # REVIEWER 2
    doc.add_heading('REVIEWER 2', level=1)
    
    doc.add_paragraph().add_run('Comment 1: ').bold = True
    doc.add_paragraph().add_run('Title correction (same as Reviewer 1)').bold = True
    
    doc.add_paragraph().add_run('Response: ').bold = True
    doc.add_paragraph('Addressed as per Reviewer 1\'s comment above.')
    
    doc.add_paragraph().add_run('Comment 2: ').bold = True
    doc.add_paragraph().add_run(
        'In the abstract, when "machine learning" is first mentioned, please introduce the acronym "ML".'
    ).bold = True
    
    doc.add_paragraph().add_run('Response: ').bold = True
    doc.add_paragraph(
        'Thank you for this suggestion. We have updated the abstract to introduce the ML acronym '
        'when machine learning is first mentioned (page 1, abstract section).'
    )
    
    doc.add_paragraph().add_run('Comment 3: ').bold = True
    doc.add_paragraph().add_run(
        'In keywords, "Remote Detection" should be "Remote Sensing".'
    ).bold = True
    
    doc.add_paragraph().add_run('Response: ').bold = True
    doc.add_paragraph(
        'Corrected. We have changed "Remote Detection" to "Remote Sensing" in the keywords section (page 1).'
    )
    
    doc.add_paragraph().add_run('Comment 4: ').bold = True
    doc.add_paragraph().add_run(
        'Please list all 19 machine learning models used and provide details about hyperparameter tuning.'
    ).bold = True
    
    doc.add_paragraph().add_run('Response: ').bold = True
    doc.add_paragraph(
        'We have added a comprehensive subsection "Machine Learning Models and Hyperparameter Optimization" '
        '(Section 2.4.1, page 6) that lists all 19 machine learning models and provides detailed information '
        'about the hyperparameter optimization process using GridSearchCV with 5-fold cross-validation.'
    )
    
    doc.add_paragraph().add_run('Comment 5: ').bold = True
    doc.add_paragraph().add_run(
        'Ground truth validation is missing. How do you validate your satellite-derived predictions?'
    ).bold = True
    
    doc.add_paragraph().add_run('Response: ').bold = True
    doc.add_paragraph(
        'TO BE DISCUSSED WITH CO-AUTHORS. We acknowledge this important limitation and have added '
        'a discussion of this issue in the "Limitations of Current Methodology" section (Section 4.1, page 10). '
        'The absence of in-situ validation data is now explicitly mentioned as a significant limitation '
        'that should be addressed in future work.'
    )
    
    doc.add_paragraph().add_run('Comment 6: ').bold = True
    doc.add_paragraph().add_run(
        'Please clarify the temporal frequency of data collection.'
    ).bold = True
    
    doc.add_paragraph().add_run('Response: ').bold = True
    doc.add_paragraph(
        'We have added clarification that all data were collected at daily temporal resolution '
        'from June 2020 to December 2022, resulting in 945 daily observations (Section 2.2, page 4).'
    )
    
    doc.add_paragraph().add_run('Comment 7: ').bold = True
    doc.add_paragraph().add_run(
        'Why was IDW interpolation chosen over kriging? Please provide justification.'
    ).bold = True
    
    doc.add_paragraph().add_run('Response: ').bold = True
    doc.add_paragraph(
        'We have enhanced the justification for IDW interpolation (Section 2.2, page 4), explaining '
        'that IDW was selected over kriging due to its computational efficiency, minimal assumptions '
        'about spatial correlation structure, and proven effectiveness in environmental applications '
        'with irregular sampling patterns.'
    )
    
    doc.add_paragraph().add_run('Comment 8: ').bold = True
    doc.add_paragraph().add_run(
        'There are inconsistencies between LST Night and LST Day in the manuscript.'
    ).bold = True
    
    doc.add_paragraph().add_run('Response: ').bold = True
    doc.add_paragraph(
        'Thank you for catching this inconsistency. We have reviewed and corrected all references '
        'to ensure consistency between LST Night and LST Day throughout the manuscript.'
    )
    
    doc.add_paragraph().add_run('Comment 9: ').bold = True
    doc.add_paragraph().add_run(
        'Please specify which model was used for SHAP analysis.'
    ).bold = True
    
    doc.add_paragraph().add_run('Response: ').bold = True
    doc.add_paragraph(
        'We have clarified that SHAP analysis was performed on the best-performing model, '
        'the Extra Trees Regressor (Section 2.5, page 6).'
    )
    
    doc.add_paragraph().add_run('Comment 10: ').bold = True
    doc.add_paragraph().add_run(
        'Please provide more detailed justification for IDW interpolation methodology.'
    ).bold = True
    
    doc.add_paragraph().add_run('Response: ').bold = True
    doc.add_paragraph('Addressed in response to Comment 7 above.')
    
    # Add remaining comments marked for co-author discussion
    remaining_comments = [
        ('Comment 11', 'Statistical significance testing', 'TO BE DISCUSSED WITH CO-AUTHORS'),
        ('Comment 12', 'Contrasting case study (low-HAB conditions)', 'TO BE DISCUSSED WITH CO-AUTHORS'),
        ('Comment 13', 'In-situ validation discussion', 'TO BE DISCUSSED WITH CO-AUTHORS'),
        ('Comment 14', 'Actionable recommendations with threshold values', 'TO BE DISCUSSED WITH CO-AUTHORS')
    ]
    
    for comment_num, comment_desc, response in remaining_comments:
        doc.add_paragraph().add_run(f'{comment_num}: ').bold = True
        doc.add_paragraph().add_run(f'{comment_desc}').bold = True
        
        doc.add_paragraph().add_run('Response: ').bold = True
        doc.add_paragraph(response)
    
    # REVIEWER 3
    doc.add_heading('REVIEWER 3', level=1)
    
    reviewer3_comments = [
        'Feature selection ecological justification',
        'Model transferability discussion', 
        'Computational requirements documentation',
        'Enhanced methodology section',
        'Expanded results interpretation',
        'Additional validation metrics',
        'Future work recommendations',
        'Enhanced discussion of limitations'
    ]
    
    for i, comment in enumerate(reviewer3_comments, 1):
        doc.add_paragraph().add_run(f'Comment {i}: ').bold = True
        doc.add_paragraph().add_run(f'{comment}').bold = True
        
        doc.add_paragraph().add_run('Response: ').bold = True
        doc.add_paragraph('TO BE DISCUSSED WITH CO-AUTHORS')
    
    # Save the document
    doc.save('Updated_Reviewer_Response_Final.docx')
    print("Updated reviewer response document created successfully!")

if __name__ == "__main__":
    create_updated_reviewer_response()
