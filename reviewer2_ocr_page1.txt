Title: Daily Prediction Model for Harmful Algae Blooms (HABs) using geospatial satellite data

Manuscript ID: water-3731451

This is a timely and promising study on HAB prediction using satellite data and machine learning. However, it
lacks in-situ validation. I recommend major revisions to improve validation, clarify my questions on methods,
and strengthen discussion.

Abstract:

Line 1: Algae Blooms should be corrected to Algal Blooms (adjective form).

Line 4: Introduce the acronym ML when first mentioning machine learning.

Line 12: In Keywords: Use “Remote Sensing” instead of “Remote Detection” it is the standard term in
the field.

Introduction and methodology

The introduction is strong and well-motivated.

Line 92: The authors mention that 19 supervised machine learning models were tested, but it doesn’t
explain which ones were used in the methods section. Although the results section lists some models (like
Random Forest, XGBoost, SVM, etc.), it’s unclear why these 19 were chosen. This should be explained
in the methods. Also, there is no information about how hyperparameters were tuned, which is important
for getting the best model performance.

Line 118: The methodology is good, but why was ground truth validation not included in the study? Field
data is essential to verify satellite-based predictions and improve the reliability of the model. Please clarify
why this step was omitted and whether any in-situ data was available.

Line 175: The authors mention using 5-fold cross-validation, but it’s unclear whether this was applied
consistently across all 19 machine learning models. Could the authors clarify if 5-fold cross-validation
was used for each model, and if not, explain the rationale for any differences in model evaluation?

Line 132: The author’s use of Inverse Distance Weighting (ID W) interpolation is mentioned, the rationale
behind choosing IDW over other spatial interpolation methods (e.g., kriging) is not explained. A brief
justification and discussion of the potential limitations of IDW would strengthen this section.

Line 154: Although the NDCI target data range is given (June 2020 — December 2022), the temporal
frequency of both the predictor and target variables (e.g., daily, weekly) is not clearly described.

The methodology does not include any in-situ validation or comparison with measured chlorophyll-a
Could the authors explain why ground truth data was not used to validate the NDCI predictions? Was such
data unavailable, or was there another reason for excluding this critical step?

There is no discussion of methodological limitations, such as the impact of cloud cover on satellite
imagery, the assumptions behind NDCI as a proxy, or the limitations of remote-only monitoring without
validation.
