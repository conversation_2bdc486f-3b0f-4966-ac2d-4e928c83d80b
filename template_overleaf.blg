This is BibTeX, Version 0.99d (TeX Live 2022/dev/Debian)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: template_overleaf.aux
The style file: Definitions/mdpi.bst
Database file #1: reference.bib
Warning--string name "sep" is undefined
--line 397 of file reference.bib
Warning--string name "jun" is undefined
--line 411 of file reference.bib
Warning--string name "jul" is undefined
--line 424 of file reference.bib
Warning--string name "jul" is undefined
--line 437 of file reference.bib
Repeated entry---line 537 of file reference.bib
 : @article{khan2021meta
 :                      ,
I'm skipping whatever remains of this entry
Repeated entry---line 727 of file reference.bib
 : @article{hoagland2006economic
 :                              ,
I'm skipping whatever remains of this entry
Repeated entry---line 736 of file reference.bib
 : @article{lapointe2015evidence
 :                              ,
I'm skipping whatever remains of this entry
Repeated entry---line 746 of file reference.bib
 : @article{khan2021meta
 :                      ,
I'm skipping whatever remains of this entry
Repeated entry---line 757 of file reference.bib
 : @article{shen2012satellite
 :                           ,
I'm skipping whatever remains of this entry
Warning--I didn't find a database entry for "sentinal2"
You've used 66 entries,
            2887 wiz_defined-function locations,
            967 strings with 18573 characters,
and the built_in function-call counts, 26464 in all, are:
= -- 2259
> -- 1624
< -- 41
+ -- 724
- -- 438
* -- 2235
:= -- 4032
add.period$ -- 191
call.type$ -- 66
change.case$ -- 66
chr.to.int$ -- 66
cite$ -- 67
duplicate$ -- 1210
empty$ -- 2162
format.name$ -- 580
if$ -- 5560
int.to.chr$ -- 1
int.to.str$ -- 66
missing$ -- 59
newline$ -- 325
num.names$ -- 192
pop$ -- 604
preamble$ -- 0
purify$ -- 66
quote$ -- 0
skip$ -- 841
stack$ -- 0
substring$ -- 1267
swap$ -- 268
text.length$ -- 4
text.prefix$ -- 1
top$ -- 0
type$ -- 455
warning$ -- 0
while$ -- 215
width$ -- 0
write$ -- 779
(There were 5 error messages)
