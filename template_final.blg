This is BibTeX, Version 0.99d (TeX Live 2022/dev/Debian)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: template_final.aux
The style file: Definitions/mdpi.bst
I found no \bibdata command---while reading file template_final.aux
Warning--I didn't find a database entry for "hill2020habnet"
Warning--I didn't find a database entry for "anderson1995ecohab"
Warning--I didn't find a database entry for "smayda1997harmful"
Warning--I didn't find a database entry for "hallegraeff1993review"
Warning--I didn't find a database entry for "ahn2006detecting"
Warning--I didn't find a database entry for "carmichael2016health"
Warning--I didn't find a database entry for "lapointe2015evidence"
Warning--I didn't find a database entry for "paerl2008blooms"
Warning--I didn't find a database entry for "fleming2014oceans"
Warning--I didn't find a database entry for "anderson2015living"
Warning--I didn't find a database entry for "anderson2009approaches"
Warning--I didn't find a database entry for "shen2012satellite"
Warning--I didn't find a database entry for "hoagland2006economic"
Warning--I didn't find a database entry for "hu2010moderate"
Warning--I didn't find a database entry for "d2012algal"
Warning--I didn't find a database entry for "tomlinson2004evaluation"
Warning--I didn't find a database entry for "hu2005red"
Warning--I didn't find a database entry for "rashidi2021monitoring"
Warning--I didn't find a database entry for "martin2009long"
Warning--I didn't find a database entry for "chellappa2008harmful"
Warning--I didn't find a database entry for "stephen2007evidence"
Warning--I didn't find a database entry for "obaid2024time"
Warning--I didn't find a database entry for "lemley2018triggers"
Warning--I didn't find a database entry for "DiasJ2015BmoH"
Warning--I didn't find a database entry for "HALLEGRAEFF2021101848"
Warning--I didn't find a database entry for "richardson1996remote"
Warning--I didn't find a database entry for "reinart2006comparison"
Warning--I didn't find a database entry for "khan2021meta"
Warning--I didn't find a database entry for "mueller1976ocean"
Warning--I didn't find a database entry for "cracknell1997advanced"
Warning--I didn't find a database entry for "stumpf2005remote"
Warning--I didn't find a database entry for "tester1998john"
Warning--I didn't find a database entry for "jensen2009remote"
Warning--I didn't find a database entry for "sentinal5"
Warning--I didn't find a database entry for "sentinal2"
Warning--I didn't find a database entry for "era5"
Warning--I didn't find a database entry for "zhao2010relation"
Warning--I didn't find a database entry for "sentinel2"
Warning--I didn't find a database entry for "shap"
Warning--I didn't find a database entry for "habnews1"
Warning--I didn't find a database entry for "habnews2"
Warning--I didn't find a database entry for "habnews3"
Warning--I didn't find a database entry for "habnews4"
Warning--I didn't find a database entry for "habnews5"
Warning--I didn't find a database entry for "pptcleanupefforts400k"
Warning--I didn't find a database entry for "fishlake"
Warning--I didn't find a database entry for "weatherlake"
Warning--I didn't find a database entry for "craborchardlake"
Warning--I didn't find a database entry for "shepard1968two"
Warning--I didn't find a database entry for "lu2008adaptive"
Warning--I didn't find a database entry for "wang2016evaluation"
Warning--I didn't find a database entry for "mishra2012development"
Warning--I didn't find a database entry for "xu2020remote"
Warning--I didn't find a database entry for "lundberg2018explainable"
Warning--I didn't find a database entry for "molnar2020interpretable"
Warning--I didn't find a database entry for "xie2020model"
Warning--I didn't find a database entry for "shi2021interpretable"
You've used 0 entries,
            2887 wiz_defined-function locations,
            615 strings with 4759 characters,
and the built_in function-call counts, 18 in all, are:
= -- 0
> -- 0
< -- 0
+ -- 0
- -- 0
* -- 0
:= -- 12
add.period$ -- 0
call.type$ -- 0
change.case$ -- 0
chr.to.int$ -- 0
cite$ -- 0
duplicate$ -- 0
empty$ -- 0
format.name$ -- 0
if$ -- 0
int.to.chr$ -- 1
int.to.str$ -- 0
missing$ -- 0
newline$ -- 3
num.names$ -- 0
pop$ -- 0
preamble$ -- 0
purify$ -- 0
quote$ -- 0
skip$ -- 0
stack$ -- 0
substring$ -- 0
swap$ -- 0
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 0
warning$ -- 0
while$ -- 0
width$ -- 0
write$ -- 2
(There was 1 error message)
