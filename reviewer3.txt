<EMAIL>
(https://susy.mdpi.com)

My Profile (/user/edit)

Logout

Journals (https://www.mdpi.com/about/journals/)

Information (https://www.mdpi.com/guidelines)

Submit (/user/manuscripts/upload)

Topics (https://www.mdpi.com/topics)

Author Services (https://www.mdpi.com/authors/english)

Initiatives

About (https://www.mdpi.com/about)

 Account 
Dashboard
(/user/myprofile)
Manage
Accounts

Journal
Manuscript ID

Password
(/user/chgpwd)

Article

Title

Daily Prediction Model for Harmful Algae Blooms (HABs) using
geospatial satellite data

Authors

<PERSON><PERSON><PERSON> , <PERSON><PERSON><PERSON> , <PERSON> *

Section

New Sensors, New Technologies and Machine Learning in Water
Sciences
(https://www.mdpi.com/journal/water/sections/new_sensors_new_t
echnologies)

Abstract

This paper implements a Daily Prediction Model for Harmful Algae
Blooms (HABs) using 1 geospatial satellite data for lakes where
deploying sensors and drones is not possible. We collected 19 2
environmental features from satellite and climate data sources to
predict the Normalized Difference 3 Chlorophyll Index (NDCI), a
well-established proxy for HABs. Nineteen different ML models
have 4 been tested. Our findings reveal that tree-based ML models
perform the best for daily prediction 5 of HABs. We go beyond
predicting a single number for HAB presence on a particular lake
but are 6 also capturing the Geospatial variability of HAB across
smaller lakes. We have tested our prediction 7 framework on
Campus Lake, Southern Illinois University (SIU), Carbondale Lake
which has faced 8 HAB problems frequently. Our study leverages
GIS and ML methodologies to provide a model which 9 will work for
any lake and output HAB magnitude with geospatial variability of
that magnitude for 10 each day. This research will aid in timely
intervention and creation of a local lake HAB alert system.

Profile
(/user/edit)
Logout

water-3731451

Type

(/user/manage_a
Change

Water (https://www.mdpi.com/journal/water) (ISSN 2073-4441)

(/user/logout)



Submissions
Submit
(/user/manuscrip
Submitted
Manuscripts
(/user/manuscrip
Co-Authored
Manuscripts
(/user/manuscrip

Author's Reply to the Review Report (Reviewer 3)
Please provide a point-by-point response to the reviewer’s comments and either enter it in
the box below or upload it as a Word/PDF file. Please enter "Please see the attachment."
in the box if you only upload an attachment. A template can be found here
(/build/attachments/Author/Example for author to respond reviewer - MDPI.docx?
a995b85).

* Author's

File

Edit

Notes to
Reviewer

Paragraph

View

Insert

Format

Tools

Table

Help

Response 1: [Type your response here and mark your revisions in
red] Thank you for pointing this out. I/We agree with this comment.
Therefore, I/we have….[Explain what change you have made.
Mention exactly where in the revised manuscript this change can be
found – page number, paragraph, and line.]“[updated text in the
man script if necessar ]”
P
0 WORDS
Word / PDF

Choose File No file chosen

Submit

Save as draft (submit later)

Review Report Form
Quality of
English
Language

(x) The English could be improved to more clearly express the
research.
( ) The English is fine and does not require any improvement.

Yes
Does the introduction provide sufficient

Can be Must be
Not
improved improved applicable

(x)

( )

( )

( )

Is the research design appropriate?

(x)

( )

( )

( )

Are the methods adequately described?

( )

( )

(x)

( )

Are the results clearly presented?

( )

( )

(x)

( )

Are the conclusions supported by the results?

(x)

( )

( )

( )

( )

(x)

( )

( )

background and include all relevant references?

Are all figures and tables clear and wellpresented?
Comments
and
Suggestions
for Authors

Dear Editor,
I have carefully reviewed the manuscript titled “Daily Prediction
Model for Harmful Algae Blooms (HABs) using geospatial satellite
data”. This manuscript presents a machine learning model to
predict daily Harmful Algal Blooms (HABs) using satellite-derived
environmental data. the model predicts the Normalized Difference
Chlorophyll Index (NDCI), a proxy for HABs, by analysing the
combination of 19 environmental indicators. Extra Trees Regressor
model showed the highest accuracy. SHAP analysis shows TP,
TSS, and EC as the major factors for HABs. The model is

successful in measuring a combination of HAB intensity and spatial
distribution and is a scalable and low-cost alternative to in-situ
monitoring; able to make important contributions to early warning
systems and lake management. Although the manuscript dealt with
an important topic needs major revision. The comments and
suggestions are below to improve the manuscript
1. Line 88–91
Comment: The use of IDW interpolation is briefly described;
however, the choice of this particular scheme is not explained,
such as why not use kriging interpolation or spline interpolation. I
suggest authors to explain why IDW interpolation was chosen over
interpolation methods.
2. Line 100–104
Comment: It is a good idea to use SHAP as feature importance, but
the methodology is not detailed. Please specify the ML model on
which SHAP was applied.
3. Line 156–182
Comment: The ML modelling pipeline is well described, but
hyperparameter tuning is not described well enough. I suggest
authors to describe whether any grid search/optimisation was
done? Did default parameters used?
4. Line 178–179
Comment: The phrase “temporal validation by training on earlier
time periods” is unclear. Please specify the date ranges used for
training vs. testing.
5. Line 315–323
Comment: The statement about scalability is less than convincing
since it is speculative in absence of validation by other lakes. I
would suggest to include a paragraph that suggests a validation
strategy of other water bodies, as evidence of this argument..
5. Line 338–343
Comment: The generic language is employed in the final
paragraph. I suggest authors for the possibilities to include
concrete follow-up activities like implementing it in another state or
lake, interconnections with early warning systems and mobile app
prototypes.
7. Line 375–385

Comment: It is important to discuss the limitations of satellite
(humidity, atmospheric noise, clouds). Please add methods to
handle these issues, e.g., gap-filling, alternative indices instead of
just saying future research should seek.
8. Line 175
Correction: “we normalize the feature set using z-score
normalization...” can be written as “we normalized the feature
set...” (past tense for consistency).
9. Line 327
Correction: “Also, based on Figure 5 we found NDCI peaks...”
should be written as “Additionally, as shown in Figure 5, NDCI
peaks were found...”

Minor grammatical errors
Line 6 - and also capture
Line 9- reframe
Line 37 - when marine and freshwater HAB impacts are combined
Line 56 -taking measurements
Line 72 - for HAB detection
Line 261 - cascading effects

Comments on
the Quality of
English
Language

Comments are included in comments section

Submission
Date

15 June 2025

Date of this
review

19 Jul 2025 15:32:01

© 1996-2025 MDPI (Basel, Switzerland) unless otherwise stated

Disclaimer
Terms and Conditions
(https://www.mdpi.com/about/terms-and-conditions)
Privacy Policy (https://www.mdpi.com/about/privacy)

