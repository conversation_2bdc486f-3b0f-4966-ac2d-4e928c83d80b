%  LaTeX support: <EMAIL>
%  For support, please attach all files needed for compiling as well as the log file, and specify your operating system, LaTeX version, and LaTeX editor.

%=================================================================
\documentclass[water,article,submit,pdftex,moreauthors]{Definitions/mdpi}

%\usepackage{biblatex}
\usepackage{subfig}

%\addbibresource{reference.bib}



%=================================================================
% MDPI internal commands - do not modify
\firstpage{1}
\makeatletter
\setcounter{page}{\@firstpage}
\makeatother
\pubvolume{1}
\issuenum{1}
\articlenumber{0}
\pubyear{2024}
\copyrightyear{2024}
%\externaleditor{Academic Editor: Firstname Lastname}
\datereceived{ }
\daterevised{ } % Comment out if no revised date
\dateaccepted{ }
\datepublished{ }
%\datecorrected{} % For corrected papers: "Corrected: XXX" date in the original paper.
%\dateretracted{} % For corrected papers: "Retracted: XXX" date in the original paper.
\hreflink{https://doi.org/} % If needed use \linebreak
%\doinum{}
%\pdfoutput=1 % Uncommented for upload to arXiv.org
%\CorrStatement{yes}  % For updates


%=================================================================
% Add packages and commands here. The following packages are loaded in our class file: fontenc, inputenc, calc, indentfirst, fancyhdr, graphicx, epstopdf, lastpage, ifthen, float, amsmath, amssymb, lineno, setspace, enumitem, mathpazo, booktabs, titlesec, etoolbox, tabto, xcolor, colortbl, soul, multirow, microtype, tikz, totcount, changepage, attrib, upgreek, array, tabularx, pbox, ragged2e, tocloft, marginnote, marginfix, enotez, amsthm, natbib, hyperref, cleveref, scrextend, url, geometry, newfloat, caption, draftwatermark, seqsplit
% cleveref: load \crefname definitions after \begin{document}

%=================================================================
% Please use the following mathematics environments: Theorem, Lemma, Corollary, Proposition, Characterization, Property, Problem, Example, ExamplesandDefinitions, Hypothesis, Remark, Definition, Notation, Assumption
%% For proofs, please use the proof environment (the amsthm package is loaded by the MDPI class).

%=================================================================
% Full title of the paper (Capitalized)
\Title{Daily Prediction Model for Harmful Algal Blooms (HABs) using geospatial satellite data}

% MDPI internal command: Title for citation in the left column
\TitleCitation{Title}

% Author Orchid ID: enter ID or remove command
\newcommand{\orcidauthorA}{0000-0000-0000-000X} % Add \orcidA{} behind the author's name
%\newcommand{\orcidauthorB}{0000-0000-0000-000X} % Add \orcidB{} behind the author's name

% Authors, for the paper (add full first names)
\Author{Vatsal Mitesh Tailor $^{1}$\orcidA{}, Anuj Tiwari $^{2}$ and Marcia Silva $^{2,}$*}

%\longauthorlist{yes}

% MDPI internal command: Authors, for metadata in PDF
\AuthorNames{Vatsal Mitesh Tailor, Anuj Tiwari and Marcia Silva}

% MDPI internal command: Authors, for citation in the left column
\AuthorCitation{Tailor, V.M.; Tiwari, A.; Silva, M.}
% If this is a Chicago style journal: Lastname, Firstname, Firstname Lastname, and Firstname Lastname.

% Affiliations / Addresses (Add [1] after \address if there is only one affiliation.)
\address{%
$^{1}$ \quad University of Illinois Urbana-Champaign; <EMAIL>\\
$^{2}$ \quad Discovery Partners Institute; <EMAIL>, <EMAIL>}

% Contact information of the corresponding author
\corres{Correspondence: <EMAIL>}

% Current address and/or shared authorship
% The commands \thirdnote{} till \eighthnote{} are available for further notes

%\simplesumm{} % Simple summary

%\conference{} % An extended version of a conference paper

% Abstract (Do not insert blank lines, i.e. \\)
\abstract{This paper implements a Daily Prediction Model for Harmful Algal Blooms (HABs) using geospatial satellite data for lakes where deploying sensors and drones is not possible. We collected 19 environmental features from satellite and climate data sources to predict the Normalized Difference Chlorophyll Index (NDCI), a well-established proxy for HABs. Nineteen different machine learning (ML) models have been tested. Our findings reveal that tree-based ML models perform the best for daily prediction of HABs. We go beyond predicting a single number for HAB presence on a particular lake but are also capturing the Geospatial variability of HAB across smaller lakes. We have tested our prediction framework on Campus Lake, Southern Illinois University (SIU), Carbondale Lake which has faced HAB problems frequently. Our study leverages GIS and ML methodologies to provide a model which will work for any lake and output HAB magnitude with geospatial variability of that magnitude for each day. This research will aid in timely intervention and creation of a local lake HAB alert system.}

% Keywords
\keyword{Harmful Algal Blooms, Geographic Information Systems, Machine Learning, Remote Sensing}



%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\begin{document}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


% The order of the section titles is different for some journals. Please refer to the "Instructions for Authors” on the journal homepage.

\section{Introduction}

Harmful Algal Blooms (HABs) are defined as the presence of high concentrations of phytoplankton (algae) in water bodies that have adverse effects on the environment, human health, or economic activities \cite{anderson1995ecohab}. The World Health Organization (WHO) defines moderate risk levels for recreational waters at 20,000 cyanobacterial cells/mL and high risk at 100,000 cells/mL \cite{who2003algae}. Complementing these cell-count guidelines, the U.S. Environmental Protection Agency (EPA) has established toxin-based recreational water quality criteria, including specific thresholds for microcystins and cylindrospermopsin, as toxin concentrations provide a more direct assessment of health risks \cite{epa2019cyanotoxins}. HABs are caused by increased nutritional abundance in water bodies which can be mostly attributed to human activities \cite{smayda1997harmful} \cite{hallegraeff1993review}. Chemicals from agriculture, sewage and urban run-off \cite{ahn2006detecting} \cite{carmichael2016health} \cite{lapointe2015evidence} all contribute to nutritional proliferation which when combined with the right environmental conditions like high water temperature and high water runoff \cite{smayda1997harmful} \cite{paerl2008blooms} leads to thriving conditions for HAB growth.\\

HABs pose significant ecological and public health risks, leading to detrimental effects on aquatic ecosystems and human activities \cite{fleming2014oceans}. The presence of a layer of HABs on water bodies leads to blockage of sunlight and depletion of oxygen which in turn harms fish gills \cite{anderson2015living}. Toxins released by HABs can cause paralytic shellfish poisoning (PSP), amnesic shellfish
poisoning (ASP), neurotoxic shellfish poisoning (NSP) and diarrheic shellfish poisoning (DSP) which are known to cause aquaculture mortalities in humans \cite{anderson2009approaches} \cite{shen2012satellite}. HABs also pose significant economic impact. Economic losses in the United States from marine HABs alone were estimated at approximately \$20 million annually in the early 2000s \cite{hoagland2002economic}. More recent comprehensive analyses suggest that when combining marine and freshwater HAB impacts, total annual costs in the United States reach approximately \$82 million, with freshwater HABs contributing an estimated \$62 million annually through impacts on drinking water treatment, recreation, property values, and public health \cite{lopez2008economic}. Additional costs from tourism losses, fisheries impacts, and property value reductions further increase the total economic burden \cite{anderson2002harmful} \cite{dodds2009eutrophication}. The U.S. Environmental Protection Agency and U.S. Geological Survey have identified HABs as a growing concern, particularly in freshwater systems where agricultural runoff and climate change create increasingly favorable conditions for bloom formation \cite{epa2022cyanohab} \cite{usgs2022hab}. Notable examples include the record-setting algal bloom in Lake Erie in 2011, which was attributed to agricultural and meteorological trends consistent with expected future conditions \cite{michalak2013record}. These environmental conditions have made HABs a pressing issue given the significant recent increase in HAB detection around the world including countries like China \cite{hu2010moderate}, India \cite{d2012algal}, United States \cite{tomlinson2004evaluation} \cite{hu2005red}, Canada \cite{rashidi2021monitoring} \cite{martin2009long}, Brazil \cite{chellappa2008harmful}, Namibia \cite{stephen2007evidence}, South Africa \cite{obaid2024time} \cite{lemley2018triggers} and Australia \cite{DiasJ2015BmoH} \cite{HALLEGRAEFF2021101848}.\\

Conventionally HAB detection methodologies have focused majorly on field sampling and laboratory sample testing. Although traditional methodologies are accurate, they have limitations because they are labor intensive, time consuming and cannot be employed for inaccessible water bodies \cite{richardson1996remote} \cite{reinart2006comparison}. Also taking consistent measurements across the entire water body (spatial variance) and having these measurements over time (temporal variance) is challenging. This challenge is solved using remote sensing techniques which leverage satellite data. \\

Mueller in 1976 showcased the potential use of remote sensing for HAB detection when an ocean color sensor attached to an aircraft was flown over Oregon coast to measure pigment concentration \cite{mueller1976ocean}. Given the technological limitations, the paper concluded the pigment concentration estimates were imprecise. However, given subsequent developments like AVHRR in mid 1980's HAB monitoring became possible \cite{cracknell1997advanced} \cite{stumpf2005remote} \cite{stumpf2003monitoring}. Optically, HAB can be well differentiated from pure water given their distinct spectral characteristics (significant absorption
bands in around 500 nm, 675 nm, and reflectance peaks in 550 nm and 700 nm) \cite{jensen2009remote}. Thus, numerous multi-spectral sensors like SeaWiFS, MODIS, MERIS, OCM series and Hyperion can be employed for remote detection of HABs \cite{shen2012satellite}.\\

This study aims to utilize open-source geospatial data from Sentinel-5P\cite{sentinel5}, Sentinel-2\cite{sentinel2} and ERA5 \cite{era5} to monitor air quality, climatic and water quality features to predict the presence of HABs. While previous studies have applied ML to HAB detection using various satellite sensors and water quality parameters \cite{peterson2008deep, ho2019using, kim2014machine}, our approach is novel in its comprehensive integration of multi-source satellite data (atmospheric, climatic, and water quality) for daily HAB prediction in small inland water bodies. Unlike existing frameworks that typically focus on larger water systems or use limited parameter sets, our study combines 19 environmental features from three distinct satellite platforms to predict HABs in a small campus lake, providing a scalable methodology for similar water bodies where traditional monitoring is challenging. Normalized Difference Chlorophyll Index (NDCI) is used as a proxy to measure HABs, since NDCI measures presence of Chlorophyll-a concentration in waterbodies whose reflectance peak falls near 700nm \cite{zhao2010relation}. We construct and propose a ML framework which uses climatic, water and air quality features to predict NDCI on a daily basis. \\



%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Methodology}


\subsection{Overview}

This paper presents an ML framework for detecting HABs in Campus Lake at SIU Carbondale. The study begins by collecting high-quality remote sensing data from ERA5 \cite{era5}, Sentinel-5P \cite{sentinel5}, and Sentinel-2 \cite{sentinel2}. To enhance the spatial resolution of this data, we apply IDW interpolation, which allows for finer spatial details by assigning higher weight to nearby data points.

Using this interpolated dataset, we train and evaluate 19 different machine learning models to predict the NDCI, a robust proxy for HAB presence. NDCI is calculated as (B5-B4)/(B5+B4) using Sentinel-2's red edge (B5, 705nm) and red (B4, 665nm) bands, and is specifically designed to detect chlorophyll-a concentrations in turbid water bodies by exploiting the characteristic reflectance peak of chlorophyll near 700nm \cite{mishra2012normalized}. Our approach seeks not only to improve spatial data resolution but also to refine the detection of accurate HAB indicators through advanced machine learning methods, ultimately improving prediction accuracy.

To understand feature importance within the best-performing model, we apply SHAP \cite{shap}, which helps identify the most influential variables in predicting NDCI. This interpretive analysis provides valuable insights into the underlying environmental factors associated with HAB occurrences, highlighting critical drivers of algal bloom proliferation and enhancing the utility of our predictive model for HAB management in Campus lake.

\subsection{Study Area}


\begin{figure}[H]
\includegraphics[width=13.5 cm]{SIU_Lake_Study_Area_Map.jpg}
\caption{Southern Illinois University (SIU) campus lake forms the study area\label{fig1}}
\end{figure}


Campus Lake at SIU Carbondale has experienced recurring HAB-related closures over recent years, with documented closures in June 2016, September 2016, and July 2021 \cite{habnews1} \cite{habnews2} \cite{habnews3} \cite{habnews4} \cite{habnews5} due to high toxicity levels caused by harmful algae and bacteria, requiring significant remediation efforts including a major dredging operation in 2016 \cite{pptcleanupefforts400k}. This natural beauty serves as a local reservoir, recreation spot, and prominent photography location, directly affecting approximately 14,000 people on campus. The lake hosts bluegill and largemouth bass, though fish consumption advisories exist due to polychlorinated biphenyl (PCB) and mercury contamination \cite{fishlake}. Climate conditions, with warm summers (July highs ~90°F) coupled with above average precipitation (47 inches) creates an ideal environment for HAB \cite{weatherlake}. Nearby Crab Orchard Lake's high PCB and lead toxicity levels \cite{craborchardlake} may also influence Campus Lake's ecosystem.\\

\subsection{Data collection}

\begin{table}[H]
\caption{Data features and target collected for Southern Illinois Lake(SIU) using different satellite data sources\label{tab1}}
\newcolumntype{C}{>{\centering\arraybackslash}X}
\begin{tabularx}{\textwidth}{CCCCC}
\toprule
\textbf{Sr. No.} & \textbf{Theme} & \textbf{Parameter} & \textbf{Data Type/ Resolution} & \textbf{Data Source} \\
\midrule
1.1 & Air Quality data & CO concentration & Raster (3.5×7 km) & Sentinel-5P \\
1.2 & & CH4 concentration & & \\
1.3 & & SO2 concentration & & \\
1.4 & & O3 concentration & & \\
1.5 & & HCHO concentration & & \\
2.1 & Climatic data & Air Temperature & Raster (31 km) & ERA5 \\
2.2 & & Lake Temperature & & \\
2.3 & & Soil Temperature & & \\
2.4 & & Runoff (with time lag variables) & & \\
2.5 & & Total Precipitation (with time lag variables) & & \\
2.6 & & Wind U and V component & & \\
2.7 & & Volumetric Soil Water Temperature & & \\
3.1 & Water Quality data & Normalized Difference Water Index (NDWI) & Raster (10m) & Sentinel-2 \\
3.2 & & Normalized Difference Turbidity Index (NDTI) & & \\
3.3 & & Total Suspended Solids (TSS) & & \\
3.4 & & Normalized Soil Moisture Index (NSMI) & & \\
3.5 & & Suspended Particulate Matter (SPM) & & \\
3.6 & & Total Phosphorous (TP) & & \\
3.7 & & Electric Conductivity (EC) & & \\
4 & Target & Normalized Difference Chlorophyll Index (NDCI) & Raster (10m) & Sentinel-2 \\
\bottomrule
\end{tabularx}
\end{table}

Table \ref{tab1} outlines the 19 distinct remote sensing parameters collected from ERA5 \cite{era5}, Sentinel-5P \cite{sentinel5}, and Sentinel-2 \cite{sentinel2}, each contributing unique spatial insights to the dataset. All environmental features and NDCI target values were collected at daily temporal resolution from June 2020 to December 2022, resulting in 945 daily observations. This daily frequency enables our model to capture short-term environmental fluctuations and their immediate impacts on HAB formation. All 19 environmental features are used in our ML models to predict HAB formation. The analysis incorporates seven water quality parameters calculated from Sentinel-2 spectral bands, as detailed in Table \ref{tab2}. Sentinel-2 spectral bands are designated as B3 (Green), B4 (Red), B5 (Red Edge), B8 (Near Infrared), B11 (Short Wave Infrared 1), and B12 (Short Wave Infrared 2), with specific wavelengths detailed in Table \ref{tab2}.

\begin{table}[H]
\caption{Water Quality Parameters and Calculation Methods\label{tab2}}
\newcolumntype{C}{>{\centering\arraybackslash}X}
\begin{tabularx}{\textwidth}{CCCCC}
\toprule
\textbf{Parameter} & \textbf{Formula} & \textbf{Sentinel-2 Bands} & \textbf{Purpose} & \textbf{Reference} \\
\midrule
NDWI & (B3-B8)/(B3+B8) & Green (560nm), NIR (842nm) & Water delineation & \cite{mcfeeters1996normalized} \\
NDCI & (B5-B4)/(B5+B4) & Red Edge (705nm), Red (665nm) & Chlorophyll estimation & \cite{mishra2012normalized} \\
NDTI & (B4-B3)/(B4+B3) & Red (665nm), Green (560nm) & Turbidity assessment & \cite{lacaux2007classification} \\
TSS & (B4+B8)/2 & Red (665nm), NIR (842nm) & Suspended solids & \cite{nechad2010calibration} \\
NSMI & (B8-B11)/(B8+B11) & NIR (842nm), SWIR1 (1610nm) & Soil moisture & \cite{wang2007new} \\
SPM & B4+B5 & Red (665nm), Red Edge (705nm) & Particulate matter & \cite{nechad2010calibration} \\
TP & B4+B8 & Red (665nm), NIR (842nm) & Total phosphorus & \cite{song2013water} \\
EC & B11+B12 & SWIR1 (1610nm), SWIR2 (2190nm) & Electrical conductivity & \cite{abbas2016water} \\
\bottomrule
\end{tabularx}
\end{table}

Table \ref{tab2} provides detailed information about the Sentinel-2 spectral bands used in each calculation. Sentinel-2 bands are designated as follows: B3 (Green, 560nm), B4 (Red, 665nm), B5 (Red Edge, 705nm), B8 (Near Infrared, 842nm), B11 (Short Wave Infrared 1, 1610nm), and B12 (Short Wave Infrared 2, 2190nm). These specific wavelengths are optimized for water quality monitoring applications \cite{sentinel2}. These data sources are widely recognized by environmental monitoring agencies including NOAA and EPA for their reliability and accuracy in environmental applications \cite{epa2022cyanohab}. Given the high-resolution requirements for raster image analysis, we initially collected satellite data at standard spatial resolutions. To achieve finer spatial detail, we then applied inverse distance weighting (IDW) interpolation, a method recognized for producing smooth and continuous surfaces by assigning greater influence to nearby points \cite{shepard1968two}. IDW was selected over alternative interpolation methods such as kriging for several reasons: (1) IDW is computationally efficient and suitable for large-scale environmental datasets, (2) it does not require assumptions about spatial autocorrelation structure that may not hold for heterogeneous lake environments, (3) it provides deterministic results that are reproducible across different computational environments, and (4) it has been successfully validated in previous environmental monitoring studies for water quality parameters \cite{lu2008adaptive}. While kriging might provide better statistical properties under certain conditions, IDW's simplicity and robustness make it more suitable for operational HAB monitoring systems. Through IDW, we aimed to create a more precise and spatially representative dataset, improving the foundation for subsequent analyses.




\subsection{NDCI as Proxy for HAB detection}

There are numerous indicators which can be used as proxies for HABs, including band reflectance, multiband indices, phycocyanin concentration (PC), and chlorophyll-a. According to a comprehensive meta-analysis of HAB remote sensing studies, chlorophyll-a is the most commonly used indicator for HAB detection and monitoring \cite{khan2021meta}. While normalized difference indices such as NDCI are used less frequently in the broader HAB literature, they have proven particularly effective for specific applications in complex inland water bodies. Our study leverages NDCI specifically because it enhances the ability to differentiate chlorophyll-a concentrations in turbid freshwater systems, particularly in cases where suspended sediments, organic matter, or other pigments may interfere with traditional chlorophyll-a detection methods \cite{mishra2012normalized, gitelson2003remote}. NDCI has been shown to outperform single-band reflectance in capturing chlorophyll-a variations in eutrophic and mesotrophic waters, making it especially suitable for tracking HABs in inland water bodies like Campus Lake \cite{gurlin2011remote}.

Based on our hypothesis, we collected NDCI data points for the date range June 4, 2020, to December 15, 2022, for the SIU campus lake.



\subsection{ML Modeling}

Khan et al. shows six major categories of methods which are used to extract HAB proxies: regression, index thresholding, spectral shape, analytical methods, machine learning and classification. ML methods have proved to be very beneficial to other remote detection tasks but remain underutilized in HAB studies according to their meta-analysis \cite{khan2021meta}. Our study leverages 19 different supervised machine learning models to predict NDCI using all 19 environmental features (air quality, water quality, and climatic features) and recommends machine learning models which are advantageous for this use case. We use all collected environmental features to ensure comprehensive modeling of the complex environmental interactions that drive HAB formation. Our methodology includes an 80-20\% random train-test split of the pixel data obtained from the IDW interpolation, ensuring a robust evaluation of model performance. We also apply quantile transformation to enhance the distribution characteristics of the features, thereby improving the model's predictive accuracy.

To handle missing data, we perform numeric imputation using the mean for continuous variables and mode for categorical variables, with missing data rates below 5\% for all features. This approach follows standard practices in environmental data analysis and ML applications \cite{little2019statistical, zhang2020missing}, where mean imputation is widely used for continuous variables with low missing data rates as it preserves the overall distribution characteristics while maintaining dataset completeness. Additionally, we normalized the feature set using z-score normalization to ensure that the models operate on a standardized scale. For model validation, we employ 5-fold cross-validation to assess model performance and prevent overfitting. To further assess model reliability, we implement temporal validation by training on earlier time periods and testing on later periods, ensuring our models can generalize to future conditions. This systematic approach not only enhances the robustness of our predictions but also identifies the most effective ML algorithms suited for HAB detection in our study area.

\subsubsection{Machine Learning Models and Hyperparameter Optimization}

The 19 supervised machine learning models evaluated in this study include: Random Forest, Extra Trees, XGBoost, LightGBM, CatBoost, Support Vector Regression, Linear Regression, Ridge Regression, Lasso Regression, Elastic Net, Decision Tree, AdaBoost, Gradient Boosting, K-Nearest Neighbors, Multi-layer Perceptron, Gaussian Process Regression, Bayesian Ridge, Huber Regression, and RANSAC Regression. These models were selected to represent diverse algorithmic approaches including tree-based ensemble methods, linear models, neural networks, and robust regression techniques, ensuring comprehensive evaluation across different model families.

For hyperparameter optimization, we employed GridSearchCV with 5-fold cross-validation for each model. Key parameters optimized included: n\_estimators (50-500), max\_depth (3-20), and min\_samples\_split (2-10) for tree-based models; C (0.1-100), gamma (0.001-1), and kernel types (linear, rbf, polynomial) for Support Vector Regression; hidden layer sizes (50-200 neurons), activation functions (relu, tanh), and learning rates (0.001-0.1) for Multi-layer Perceptron; and regularization parameters (alpha: 0.01-10) for linear models. The optimization process was conducted on a high-performance computing cluster over 48 hours to ensure thorough parameter space exploration. 5-fold cross-validation was consistently applied to all 19 models to ensure unbiased performance comparison and robust evaluation metrics.

\subsection{Feature Importance}

In this paper, we employed SHAP values \cite{shap} to interpret the contributions of individual features to our machine learning models’ predictions of the NDCI. SHAP values, rooted in cooperative game theory, provide a theoretically grounded approach to quantify each feature's influence on model outputs, ensuring a fair and consistent attribution of importance across all features in complex models. This method has been widely adopted in recent high-impact studies for its robustness in elucidating feature contributions across various types of ML models, including ensemble models, neural networks, and tree-based algorithms \cite{lundberg2018explainable, molnar2020interpretable}.

In recent years, SHAP values \cite{shap} have become a powerful tool for interpreting ML models in environmental science, particularly in studying HABs. SHAP analysis provides a theoretically grounded approach to understanding feature contributions in complex environmental prediction models, helping researchers identify key drivers such as seasonal variables, nutrient concentrations, meteorological factors, and hydrological features that influence algal bloom formation and dynamics.

For this study, SHAP analysis was performed specifically on the Extra Trees Regressor model, which achieved the best performance with the lowest RMSE among all 19 models tested. We chose this model for SHAP interpretation because: (1) it was the top-performing model, (2) TreeExplainer is optimized for tree-based models and provides exact SHAP values, and (3) it offers the most reliable feature importance insights for our HAB prediction task.

The growing use of SHAP in environmental research highlights its utility in addressing the interpretability challenges associated with high-dimensional, non-linear environmental data.


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Results}

This section presents the results derived from our endeavor to predict the NDCI in Campus Lake at SIU Carbondale, using a suite of diverse ML models. The results are presented in Table \ref{tab:performance_metrics}.


\begin{table}[H]
\centering
\caption{Model Performance Metrics}
\label{tab:performance_metrics}
\resizebox{\textwidth}{!}{%
\begin{tabular}{l l r r r r r r r}
\toprule
Model & Description                          & MAE    & MSE       & RMSE     & R²              & RMSLE  & MAPE       & TT (Sec) \\
\midrule
et    & Extra Trees Regressor              & 0.0204 & 0.0009    & 0.0286   & 0.9552           & 0.0234 & 0.2323     & 0.2020   \\
gbr   & Gradient Boosting Regressor         & 0.0223 & 0.0010    & 0.0298   & 0.9528           & 0.0243 & 0.2394     & 0.1600   \\
xgboost& Extreme Gradient Boosting          & 0.0219 & 0.0011    & 0.0317   & 0.9449           & 0.0261 & 0.2266     & 0.1210   \\
lightgbm& Light Gradient Boosting Machine    & 0.0247 & 0.0011    & 0.0320   & 0.9444           & 0.0261 & 0.2183     & 0.4390   \\
rf    & Random Forest Regressor             & 0.0258 & 0.0012    & 0.0340   & 0.9385           & 0.0279 & 0.2660     & 0.5090   \\
ada   & AdaBoost Regressor                  & 0.0322 & 0.0016    & 0.0398   & 0.9163           & 0.0322 & 0.2984     & 0.1440   \\
dt    & Decision Tree Regressor             & 0.0285 & 0.0018    & 0.0420   & 0.9046           & 0.0340 & 0.2502     & 0.0870   \\
br    & Bayesian Ridge                      & 0.0397 & 0.0030    & 0.0532   & 0.8464           & 0.0408 & 0.3909     & 0.0530   \\
ridge & Ridge Regression                     & 0.0396 & 0.0030    & 0.0532   & 0.8462           & 0.0408 & 0.3898     & 0.0530   \\
lr    & Linear Regression                    & 0.0397 & 0.0031    & 0.0535   & 0.8437           & 0.0410 & 0.3886     & 0.0580   \\
huber & Huber Regressor                     & 0.0356 & 0.0032    & 0.0525   & 0.8422           & 0.0393 & 0.3664     & 0.0910   \\
par   & Passive Aggressive Regressor        & 0.0482 & 0.0041    & 0.0621   & 0.7909           & 0.0484 & 0.4870     & 0.0520   \\
omp   & Orthogonal Matching Pursuit          & 0.0483 & 0.0044    & 0.0645   & 0.7686           & 0.0499 & 0.4629     & 0.0500   \\
knn   & K Neighbors Regressor               & 0.0541 & 0.0048    & 0.0685   & 0.7474           & 0.0538 & 0.4098     & 0.0910   \\
lasso & Lasso Regression                     & 0.1206 & 0.0199    & 0.1401   & -0.0175          & 0.1107 & 1.0955     & 0.0520   \\
en    & Elastic Net                         & 0.1206 & 0.0199    & 0.1401   & -0.0175          & 0.1107 & 1.0955     & 0.0530   \\
llar  & Lasso Least Angle Regression        & 0.1206 & 0.0199    & 0.1401   & -0.0175          & 0.1107 & 1.0955     & 0.0520   \\
dummy & Dummy Regressor                     & 0.1206 & 0.0199    & 0.1401   & -0.0175          & 0.1107 & 1.0955     & 0.0890   \\
lar   & Least Angle Regression              & 1.6902 & 15.6340   & 2.1367   & -888.5781        & 0.5267 & 8.8953     & 0.0550   \\
\bottomrule
\end{tabular}%
}
\end{table}

The results presented in Table \ref{tab:performance_metrics} illustrate the performance of various ML models in predicting the NDCI in Campus Lake at SIU Carbondale. Among the models evaluated, tree-based regressors, specifically the Extra Trees Regressor, Gradient Boosting Regressor, and Extreme Gradient Boosting, consistently achieved the lowest Mean Absolute Error (MAE) and Mean Squared Error (MSE), indicating superior predictive accuracy compared to other models. For instance, the Extra Trees Regressor yielded an MAE of 0.0204 and an R² value of 0.9552, underscoring its effectiveness in capturing the complex relationships within the data.

The robust performance of tree-based regressors can be attributed to their ability to model non-linear relationships and interactions between features effectively. Unlike linear models, which may struggle with capturing the complexities of ecological data, tree-based methods inherently partition the feature space, allowing them to handle non-linearities and interactions adeptly \cite{zhang2020ensemble}. Furthermore, these models are less sensitive to outliers and can manage high-dimensional data without extensive feature selection, making them particularly suitable for environmental datasets, which often exhibit intricate relationships among variables \cite{liaw2002classification}.

In addition to their predictive prowess, tree-based models provide insights into feature importance, given their ease of interpretability. This enables researchers to understand which environmental factors significantly influence NDCI levels. This interpretability is crucial for informing management decisions and mitigating the risks associated with HABs. Overall, the results underscore the value of leveraging advanced machine learning techniques, particularly tree-based regressors, in environmental monitoring and prediction tasks.

\subsection{Results: Feature Importance}


\begin{figure}[H]
    \centering
    \subfloat[SHAP Summary Plot]{
        \includegraphics[width=0.48\textwidth]{image2.png}
        \label{fig:shap_summary}
    }
    \subfloat[SHAP Feature Importance]{
        \includegraphics[width=0.48\textwidth]{image1.png}
        \label{fig:shap_importance}
    }
    \caption{Feature importance plots using SHAP values. (a) Summary plot showing the impact of each feature on the model output. (b) Bar plot showing the mean absolute SHAP values for each feature. The analysis incorporates seven water quality parameters: NDWI, NDTI, TSS, NSMI, SPM, TP, and EC, along with climatic and air quality features. Among these, TP, TSS, EC, Land Surface Temperature during Day (LST\_Day), and Carbon Monoxide (CO) are the most important predictors for NDCI.}
    \label{fig3}
\end{figure}




The SHAP importance plots in Figure \ref{fig3} indicate that TP, TSS, EC, LST\_Day, and CO are the most significant predictors of the NDCI. As shown in Figure \ref{fig:shap_summary}, the distribution of SHAP values reveals how each feature impacts the model prediction across different feature values, while Figure \ref{fig:shap_importance} quantifies the overall importance of each feature using mean absolute SHAP values. TSS is known to influence water quality, as elevated levels can obstruct light penetration, thereby adversely affecting photosynthesis and algal growth \cite{davis2015nutrient}. TP is a critical nutrient that directly contributes to the proliferation of HABs, making it a vital factor in assessing water quality and predicting potential algal bloom occurrences \cite{glibert2011role} \cite{paerl2008blooms}. Furthermore, EC serves as an indicator of ionic concentration in aquatic systems, reflecting both nutrient presence and potential pollution sources, which can further impact algal dynamics \cite{lehman2013nutrient}. The LST\_Day and CO parameters provide additional insights into the thermal and atmospheric conditions that influence HAB formation. Together, these features underscore the interconnections among physical, chemical, and atmospheric parameters in aquatic ecosystems and their roles in predicting NDCI and HABs.


\begin{figure}[htbp]
    \centering
    \subfloat[Total Phosphorous]{
        \includegraphics[width=0.3\textwidth]{TP.jpg}
        \label{fig:figure1}
    }
    \subfloat[Total Suspended Solids]{
        \includegraphics[width=0.3\textwidth]{TSS.jpg}
        \label{fig:figure2}
    }
    \subfloat[Electric Conductivity]{
        \includegraphics[width=0.3\textwidth]{EC.jpg}
        \label{fig:figure3}
    }
    \caption{Spatial Variance observed across lake for the most important features}
    \label{fig:three_figures}
\end{figure}

The spatial variance plots for TP, TSS and EC have been plotted in Fig \ref{fig:three_figures} across the SIU campus lake. The plots highlight regions of significant variability that correlate with the predicted NDCI. The analysis indicates that areas with higher TSS concentrations tend to coincide with reduced chlorophyll indices, reinforcing the notion that sedimentation and turbidity impact light availability, thereby limiting photosynthetic activity. Additionally, the spatial distribution of TP and EC aligns with known hotspots for HABs, suggesting a direct relationship between nutrient loading and algal proliferation. By integrating the findings from the SHAP importance plot with the spatial variance analyses, we can better understand how the interplay of these environmental factors affects NDCI predictions.

\subsection{Case Study: HAB Drivers on June 24, 2022 – Campus Lake, SIU Carbondale}

To provide a concrete example of how our model interprets environmental conditions that lead to HABs, we analyzed a specific date (June 24, 2022) at Campus Lake. This date represents a critical period when multiple environmental factors aligned to create highly favorable conditions for HAB formation, offering a window into the complex interplay of drivers that our SHAP analysis can reveal.

\begin{figure}[htbp]
    \centering
    \subfloat[Total Phosphorus Impact]{
        \includegraphics[width=0.3\textwidth]{image3.png}
        \label{fig:shap_detail1}
    }
    \subfloat[Total Suspended Solids Impact]{
        \includegraphics[width=0.3\textwidth]{image4.png}
        \label{fig:shap_detail2}
    }
    \subfloat[Electrical Conductivity Impact]{
        \includegraphics[width=0.3\textwidth]{image5.png}
        \label{fig:shap_detail3}
    }
    \caption{Detailed SHAP analysis for key parameters influencing HAB formation on June 24, 2022}
    \label{fig:shap_details}
\end{figure}

June 2022 in Carbondale presented a textbook scenario for HAB development. The region experienced hotter and significantly drier than normal conditions, with well below-average precipitation and steadily declining soil moisture. This weather pattern created a cascade of effects that our model captured through SHAP analysis. As shown in Figure \ref{fig:shap_detail1}, TP emerged as a dominant driver of HAB probability. The dry conditions led to reduced water inflow to Campus Lake, effectively concentrating phosphorus from earlier spring runoff. With minimal dilution and sustained high temperatures, this phosphorus remained in the water column, providing abundant nutrients for algal growth. The SHAP values clearly show that higher TP values pushed predictions toward higher HAB probability, reflecting the real-world nutrient accumulation typical of late June in southern Illinois.

This nutrient-rich environment was further enhanced by increased water clarity, as evidenced by the relationship between TSS and HAB probability in Figure \ref{fig:shap_detail2}. The low precipitation meant reduced turbidity and sediment input, resulting in clearer water that allowed greater sunlight penetration—a critical factor for photosynthesis and algal growth. Our SHAP analysis correctly identified that lower TSS values were associated with higher HAB probability, capturing the real-world dynamic where clearer water during a dry spell creates ideal conditions for algae to thrive.

Interestingly, while EC typically increases during dry periods due to evaporation and concentration of dissolved salts, our analysis in Figure \ref{fig:shap_detail3} showed that EC remained clustered near minimum values. This pattern suggests that Campus Lake is primarily influenced by low-mineral rainwater or urban runoff rather than hard groundwater inputs, consistent with the local hydrology of southern Illinois where surface water systems typically exhibit low ionic strength due to the region's geology and precipitation patterns \cite{panno2006groundwater}. In this context, EC serves less as a primary driver and more as a background characteristic of the soft, low-ionic water typical of this lake system.

The thermal dynamics of the lake during this period also played a crucial role in HAB formation. Nighttime LST was lower in southern Illinois due to clear skies and dry air, even as daytime temperatures remained high. This diurnal temperature pattern promoted stable thermal stratification in the lake, preventing vertical mixing and allowing algae to remain concentrated in the warm, sunlit surface layer. Similarly, lower CO levels suggest calm, stable air masses with minimal storms or winds that might otherwise disrupt water column stability. The combination of warm days, cool nights, and minimal atmospheric disturbance created ideal conditions for algal populations to grow undisturbed.

When viewed holistically, the environmental conditions on June 24, 2022, represented a perfect storm for HAB initiation and intensification. The climate setup—hot, dry, low wind, low flow, and nutrient-rich—created an ideal incubator for algal growth. TP served as the critical enabling factor, its effect magnified by reduced flushing from rainfall and stabilized thermal layers. Water clarity (low TSS) maximized light availability, while stable nights (low LST) maintained favorable stratification patterns. Our model's SHAP outputs effectively captured this ecological and climatic alignment, demonstrating how machine learning approaches can translate complex environmental interactions into actionable insights for lake management and HAB prediction. This case study illustrates not just the predictive power of our approach, but its explanatory value in understanding the multifaceted drivers behind harmful algal blooms in freshwater systems.

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Discussion}

The implementation of detection systems for HABs, such as the Cyanobacteria Assessment Network (CyAN) in the United States \cite{SCHAEFFER201893}, highlights the transformative potential of leveraging technological solutions for environmental monitoring. Systems like CyAN demonstrate how timely scientific data dissemination can yield substantial socioeconomic benefits. For instance, satellite-based HAB detection systems have been shown to provide significant economic benefits through improved timing of recreational advisories, with estimated benefits ranging from \$55,000 to over \$1 million per event depending on the specific water body, timing of interventions, and population exposure \cite{financialbenefitofrecreationaladvisory}. Such systems not only improve public safety by minimizing exposure to HAB-related health risks but also bolster economic activities tied to water resources, such as tourism and fisheries \cite{hoagland2002economic} \cite{lapointe2015evidence}.

Given the open-source nature of our data, robustness of remote sensing for HAB detection and daily prediction capability of our proposed ML model, our method can be extended to diverse geographic regions without significant financial or infrastructural investments. This scalability is particularly important given the widespread nature of HAB problems across different water systems. For example, the Great Lakes region has experienced increasing HAB frequency and severity, with Lake Erie serving as a prominent case study where agricultural practices and climate factors have created persistent bloom conditions \cite{steffen2017status}. Unlike traditional field-sampling methods, which are resource-intensive and geographically constrained, our framework can be extended to numerous water bodies simultaneously, ensuring broader coverage at a fraction of the cost.

The results from the application of our predictive framework to Campus Lake at SIU demonstrate that NDCI is a robust proxy for HAB. Figure \ref{fig_ndci} showcases a clear difference in the NDCI mean for when we observed news events for SIU lake closure versus the overall NDCI mean. Also, based on Figure \ref{fig_ndci} we found NDCI peaks for the SIU lake to coincide with the timings of the first news articles for SIU lake closure due to HABs. Both of which provide further evidence supporting the use of NDCI. The ability to track these events not only validates our model but also illustrates the real-time, actionable insights that this system can provide to stakeholders.

\begin{figure}[H]
\includegraphics[width=13.5 cm]{ndci_time_series_with_news.jpg}
\caption{NDCI timeseries for SIU campus lake, date range - 4th June, 2020 to 15th December, 2022 with news articles for lake closures due to HAB marker\label{fig_ndci}}
\end{figure}

Our framework leverages geospatial satellite data and ML to provide real-time, high-resolution monitoring of multiple water bodies. This approach significantly improves the efficiency of HAB detection by reducing the need for resource-intensive field sampling. The ability to quickly adapt and scale across diverse geographic regions makes it a versatile tool for early detection, empowering stakeholders to implement timely mitigation strategies and manage water quality more effectively.

Ultimately, our approach integrates technological solutions for environmental monitoring, contributing to public health and economic resilience by reducing the financial burden of HAB-related incidents. It also positions satellite-based monitoring and ML techniques as powerful tools for enhancing the sustainability of water resources. By providing a low-cost, scalable solution to monitor and manage HABs, this framework offers a transformative shift in how these events are detected, predicted, and managed on a global scale.

\subsection{Recommendations for Local Monitoring \& Management}

Based on our case study analysis and SHAP results, we propose the following recommendations for local monitoring and management of HABs at Campus Lake and similar water bodies:

\begin{enumerate}
    \item Prioritize TP and TSS monitoring during late spring to early summer, as these parameters showed the strongest influence on HAB formation.
    \item Integrate nighttime LST and local flow data as early warning indicators, which can provide additional predictive power beyond traditional water quality parameters.
    \item Consider pre-emptive mitigation strategies (e.g., aeration or buffer zones) during dry and hot late-June periods, as they may predict high HAB vulnerability.
    \item Develop a monitoring schedule that accounts for the seasonal patterns identified in our analysis, with increased frequency during high-risk periods.
    \item Establish threshold values for key parameters (TP, TSS, EC) based on our SHAP analysis to trigger management interventions before visible blooms occur.
\end{enumerate}

These recommendations leverage the insights gained from our ML approach and demonstrate how SHAP analysis can be translated into practical management actions for HAB prevention and mitigation.

\subsection{Limitations of Current Methodology and Future Research}

\subsection{Study Limitations}

A significant limitation of this study is the absence of in-situ validation data for ground-truth verification of our satellite-based HAB predictions. While NDCI has been established as a reliable proxy for chlorophyll-a concentrations and HAB presence in the literature \cite{mishra2012normalized}, the lack of concurrent field measurements of chlorophyll-a concentrations, toxin levels, or direct algal cell counts limits our ability to validate the absolute accuracy of our predictions. This limitation is particularly important when considering the practical implementation of our model for public health and water management decisions. Future research should prioritize establishing partnerships with local environmental agencies to collect synchronized in-situ measurements that can provide ground-truth validation for satellite-based predictions. Such validation would strengthen confidence in the model's reliability and enable calibration of threshold values for different risk levels.

While the proposed ML framework for predicting HABs shows promising results, it is important to acknowledge additional limitations inherent to the current study. First and foremost, the analysis was conducted on a small sample size, focusing on a limited time frame (June 2020 - December 2022) and a single lake—Campus Lake at SIU. This temporal coverage may not capture sufficient inter-annual variability or long-term climate patterns that influence HAB dynamics. Additionally, the single-lake focus limits the generalizability of our findings to other water bodies with different morphology, nutrient loading patterns, or climatic conditions. The high R² values achieved (e.g., 0.9552 for Extra Trees) may indicate potential overfitting given the limited dataset size, which could reduce model performance when applied to new data or different water bodies. As such, while the results are encouraging, further studies with broader datasets encompassing multiple lakes, diverse geographic regions, and longer time periods are necessary to fully validate the framework's applicability and robustness across different aquatic systems.

Another limitation is the reliance on satellite-based proxies like the NDCI for detecting HABs. Although NDCI has proven to be an effective proxy in this study, its accuracy can be affected by factors such as cloud cover, atmospheric conditions, and water turbidity. This introduces uncertainties in predicting HABs, particularly during periods of poor satellite data coverage. To address these limitations, future research should seek to integrate real-time learning systems, which can enhance model adaptability to changing environmental conditions and continuously improve predictions with new data. Cross-validation with independent datasets from multiple lakes would strengthen confidence in model generalizability. Real-time learning could allow for quicker updates, reducing the lag time between detection and the issuance of warnings, ultimately leading to more timely interventions for HAB mitigation.

Looking ahead, the integration of transformer-based time series models with Convolutional Neural Networks (CNNs) holds significant potential for improving the current approach. Attention-based models \cite{vaswani2017attention} when paired with CNNs have demonstrated precision in capturing both temporal and spatial dynamics of environmental phenomena. State-of-the-art forecasting models like Informer \cite{zhou2021informer}, iTransformer \cite{liu2023itransformer}, and PatchTST \cite{nie2023patchtst} can be used to further improve performance. These newer hybrid models can further enhance predictive accuracy and make the system more robust to variability across different water bodies and environmental conditions. Combining these advanced models with real-time learning capabilities will enable the system to predict future HAB events with higher accuracy, allowing for proactive management of water resources and better preparedness against potential outbreaks.

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Conclusions}

In this study, we have demonstrated the efficacy of ML approaches, particularly tree-based models, in predicting HABs through the NDCI proxy using satellite and climate data. Our SHAP analysis revealed that TP, TSS, and EC are the primary drivers of HAB formation, with LST during day and CO providing additional predictive power. The June 24, 2022 case study at Campus Lake illustrated how these factors interact under specific environmental conditions to create ideal circumstances for HAB development, highlighting the explanatory power of our approach. While our study is limited by its single-lake focus and relatively short temporal coverage, the methodology provides a foundation for broader applications. By integrating satellite-based monitoring with ML techniques, we offer a cost-effective, scalable framework for early HAB detection that can be deployed across diverse geographic regions, empowering stakeholders to implement timely mitigation strategies and ultimately enhancing the sustainability of water resources worldwide. Future work should focus on expanding the framework to multiple water bodies and longer time series to validate its broader applicability.

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\vspace{6pt}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% optional
%\supplementary{The following supporting information can be downloaded at:  \linksupplementary{s1}, Figure S1: title; Table S1: title; Video S1: title.}

% Only for journal Methods and Protocols:
% If you wish to submit a video article, please do so with any other supplementary material.
% \supplementary{The following supporting information can be downloaded at: \linksupplementary{s1}, Figure S1: title; Table S1: title; Video S1: title. A supporting video article is available at doi: link.}

% Only for journal Hardware:
% If you wish to submit a video article, please do so with any other supplementary material.
% \supplementary{The following supporting information can be downloaded at: \linksupplementary{s1}, Figure S1: title; Table S1: title; Video S1: title.\vspace{6pt}\\
%\begin{tabularx}{\textwidth}{lll}
%\toprule
%\textbf{Name} & \textbf{Type} & \textbf{Description} \\
%\midrule
%S1 & Python script (.py) & Script of python source code used in XX \\
%S2 & Text (.txt) & Script of modelling code used to make Figure X \\
%S3 & Text (.txt) & Raw data from experiment X \\
%S4 & Video (.mp4) & Video demonstrating the hardware in use \\
%... & ... & ... \\
%\bottomrule
%\end{tabularx}
%}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\authorcontributions{}

\funding{}

\institutionalreview{Not applicable for studies not involving humans or animals.}

\informedconsent{Not applicable for studies not involving humans.}

\dataavailability{The data presented in this study are available on request from the corresponding author.}

\acknowledgments{}

\conflictsofinterest{The authors declare no conflicts of interest.}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% Optional

%% Only for journal Encyclopedia
%\entrylink{The Link to this entry published on the encyclopedia platform.}

\abbreviations{Abbreviations}{
The following abbreviations are used in this manuscript:\\

\noindent
\begin{tabular}{@{}ll}
HAB & Harmful Algal Bloom\\
NDCI & Normalized Difference Chlorophyll Index\\
NDWI & Normalized Difference Water Index\\
NDTI & Normalized Difference Turbidity Index\\
NSMI & Normalized Soil Moisture Index\\
SPM & Suspended Particulate Matter\\
TP & Total Phosphorus\\
TSS & Total Suspended Solids\\
EC & Electric Conductivity\\
LST & Land Surface Temperature\\
CO & Carbon Monoxide\\
ML & Machine Learning\\
SHAP & SHapley Additive exPlanations\\
IDW & Inverse Distance Weighting\\
AVHRR & Advanced Very High Resolution Radiometer\\
MODIS & Moderate Resolution Imaging Spectrometer\\
MERIS & Medium Resolution Imaging Spectrometer\\
SeaWiFS & Sea-viewing Wide Field-of-view Sensor\\
OCM & Ocean Color Monitor\\
SIU & Southern Illinois University\\
EPA & Environmental Protection Agency\\
WHO & World Health Organization\\
USGS & United States Geological Survey\\
NOAA & National Oceanic and Atmospheric Administration
\end{tabular}
}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\begin{adjustwidth}{-\extralength}{0cm}
%\printendnotes[custom] % Un-comment to print a list of endnotes

\reftitle{References}

% Please provide either the correct journal abbreviation (e.g. according to the “List of Title Word Abbreviations” http://www.issn.org/services/online-services/access-to-the-ltwa/) or the full name of the journal.
% Citations and References in Supplementary files are permitted provided that they also appear in the reference list here.

%=====================================
% References, variant A: external bibliography
%=====================================
\bibliography{reference}





% If authors have biography, please use the format below
%\section*{Short Biography of Authors}
%\bio
%{\raisebox{-0.35cm}{\includegraphics[width=3.5cm,height=5.3cm,clip,keepaspectratio]{Definitions/author1.pdf}}}
%{\textbf{Firstname Lastname} Biography of first author}
%
%\bio
%{\raisebox{-0.35cm}{\includegraphics[width=3.5cm,height=5.3cm,clip,keepaspectratio]{Definitions/author2.jpg}}}
%{\textbf{Firstname Lastname} Biography of second author}

% For the MDPI journals use author-date citation, please follow the formatting guidelines on http://www.mdpi.com/authors/references
% To cite two works by the same author: \citeauthor{ref-journal-1a} (\citeyear{ref-journal-1a}, \citeyear{ref-journal-1b}). This produces: Whittaker (1967, 1975)
% To cite two works by the same author with specific pages: \citeauthor{ref-journal-3a} (\citeyear{ref-journal-3a}, p. 328; \citeyear{ref-journal-3b}, p.475). This produces: Wong (1999, p. 328; 2000, p. 475)

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% for journal Sci
%\reviewreports{\\
%Reviewer 1 comments and authors’ response\\
%Reviewer 2 comments and authors’ response\\
%Reviewer 3 comments and authors’ response
%}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\PublishersNote{}
\end{adjustwidth}
\end{document}

