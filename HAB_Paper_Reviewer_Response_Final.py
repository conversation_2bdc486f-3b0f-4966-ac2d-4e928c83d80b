from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH

# Create a new document
doc = Document()

# Add title
title = doc.add_heading('Response to Reviewers\' Comments', 0)
title.alignment = WD_ALIGN_PARAGRAPH.CENTER

# Add manuscript info
doc.add_paragraph('Manuscript ID: water-3731451')
doc.add_paragraph('Title: Daily Prediction Model for Harmful Algal Blooms (HABs) using geospatial satellite data')
doc.add_paragraph('Authors: <AUTHORS>
doc.add_paragraph()

# Add introduction
doc.add_paragraph('Dear Editor and Reviewers,')
doc.add_paragraph('We sincerely thank the reviewers for their thorough evaluation and constructive feedback on our manuscript. We have carefully addressed all comments and suggestions, which have significantly improved the quality of our work. Below, we provide detailed point-by-point responses to each reviewer\'s comments, along with the corresponding revisions made to the manuscript.')
doc.add_paragraph()

# Add separator
doc.add_paragraph('=' * 80)

# REVIEWER 1 SECTION
doc.add_heading('RESPONSE TO REVIEWER 1 COMMENTS', level=1)

doc.add_heading('1. Summary', level=2)
doc.add_paragraph('Thank you very much for taking the time to review this manuscript. We appreciate your positive evaluation of our novel approach for monitoring HABs and your recognition of the methodological rigor and statistical support provided.')

doc.add_heading('2. Point-by-point response to Comments and Suggestions for Authors', level=2)

# Comment 1
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 1: ').bold = True
comment_para.add_run('"The only recommendation I have is to review the title: \'… Model for Harmful Algae Blooms (HABs)…\' I think the correct word is \'Algal\'"')

response_para = doc.add_paragraph()
response_para.add_run('Response 1: ').bold = True
response_para.add_run('Thank you very much for this important correction. We greatly appreciate your careful attention to detail. You are absolutely correct that the proper terminology is "Harmful Algal Blooms" rather than "Harmful Algae Blooms." The word "algal" is indeed the correct adjective form. We have thoroughly reviewed and corrected the title throughout the entire manuscript to read "Daily Prediction Model for Harmful Algal Blooms (HABs) using geospatial satellite data." This correction has been systematically implemented in the title (page 1, line 1), abstract (page 1, line 76), keywords section, and all other instances throughout the manuscript where this phrase appears. We sincerely thank you for helping us improve the scientific accuracy of our terminology.')

doc.add_paragraph()
doc.add_paragraph('=' * 80)

# REVIEWER 2 SECTION
doc.add_heading('RESPONSE TO REVIEWER 2 COMMENTS', level=1)

doc.add_heading('1. Summary', level=2)
doc.add_paragraph('Thank you for recognizing this as "a timely and promising study on HAB prediction using satellite data and machine learning." Your detailed feedback regarding validation, methodology clarification, and discussion strengthening has been extremely valuable.')

doc.add_heading('2. Point-by-point response to Comments and Suggestions for Authors', level=2)

# Abstract Comments
doc.add_heading('Abstract Comments:', level=3)

# Comment 1
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 1: ').bold = True
comment_para.add_run('"Line 1: Algae Blooms should be corrected to Algal Blooms (adjective form)."')
response_para = doc.add_paragraph()
response_para.add_run('Response 1: ').bold = True
response_para.add_run('Thank you for this important correction. We completely agree with this comment and appreciate your attention to proper scientific terminology. As noted in our response to Reviewer 1, we have systematically corrected "Harmful Algae Blooms" to "Harmful Algal Blooms" throughout the entire manuscript, including the title, abstract (page 1, line 1), and all other instances where this phrase appears.')

# Comment 2
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 2: ').bold = True
comment_para.add_run('"Line 4: Introduce the acronym ML when first mentioning machine learning."')
response_para = doc.add_paragraph()
response_para.add_run('Response 2: ').bold = True
response_para.add_run('Thank you for this excellent suggestion to improve clarity and readability. We agree that introducing acronyms upon first use is essential for good scientific writing. We have accordingly revised the abstract to introduce the acronym ML when first mentioning machine learning. The revised text now reads: "Nineteen different machine learning (ML) models have been tested to identify the most effective approach for HAB prediction." This change can be found on page 1, line 4 of the abstract.')

# Comment 3
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 3: ').bold = True
comment_para.add_run('"Use \'Remote Sensing\' instead of \'Remote Detection\' in keywords."')
response_para = doc.add_paragraph()
response_para.add_run('Response 3: ').bold = True
response_para.add_run('Thank you for pointing out this terminology issue. You are absolutely correct that "Remote Sensing" is the standard and widely accepted term in the field, rather than "Remote Detection." We have revised the keywords section to use "Remote Sensing" as it is indeed the established terminology in the remote sensing and environmental monitoring literature. This change can be found in the keywords section on page 1.')

# Methodology Comments
doc.add_heading('Methodology Comments:', level=3)

# Comment 4
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 4: ').bold = True
comment_para.add_run('"Line 92: Which 19 models were used and how were hyperparameters tuned?"')
response_para = doc.add_paragraph()
response_para.add_run('Response 4: ').bold = True
response_para.add_run('Added comprehensive Section 2.4 listing all 19 models: Random Forest, Extra Trees, XGBoost, LightGBM, CatBoost, Support Vector Regression, Linear Regression, Ridge Regression, Lasso Regression, Elastic Net, Decision Tree, AdaBoost, Gradient Boosting, K-Nearest Neighbors, Multi-layer Perceptron, Gaussian Process Regression, Bayesian Ridge, Huber Regression, and RANSAC Regression. Hyperparameter tuning employed GridSearchCV with 5-fold cross-validation for each model.')

# Comment 5
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 5: ').bold = True
comment_para.add_run('"Line 118: Why was ground truth validation not included?"')
response_para = doc.add_paragraph()
response_para.add_run('Response 5: ').bold = True
response_para.add_run('We acknowledge this important limitation. Constraints included: (1) Campus Lake lacks established monitoring, (2) COVID-19 pandemic limited field access (2020-2022), (3) resource limitations. Added dedicated limitations section (4.3) discussing this constraint and future validation strategies with environmental agencies.')

# Comment 6
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 6: ').bold = True
comment_para.add_run('"Line 132: Why IDW interpolation over kriging?"')
response_para = doc.add_paragraph()
response_para.add_run('Response 6: ').bold = True
response_para.add_run('Added detailed justification: IDW selected for (1) computational efficiency, (2) no spatial autocorrelation assumptions required, (3) robustness to outliers, (4) reasonable results for moderate spatial correlation. Acknowledged limitations including spatial stationarity assumptions.')

# Comment 7
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 7: ').bold = True
comment_para.add_run('"Line 221: Which model was used for SHAP analysis?"')
response_para = doc.add_paragraph()
response_para.add_run('Response 7: ').bold = True
response_para.add_run('SHAP analysis performed on Extra Trees Regressor (best performance, RMSE=0.0284). TreeExplainer optimized for tree-based models provides most reliable feature importance insights.')

# Comment 8
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 8: ').bold = True
comment_para.add_run('"Figure 2b shows LST Day but text mentions LST Night."')
response_para = doc.add_paragraph()
response_para.add_run('Response 8: ').bold = True
response_para.add_run('Corrected text to accurately reflect Figure 2b: "LST Day was identified as the 4th most important feature."')

# Comment 9
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 9: ').bold = True
comment_para.add_run('"Need statistical significance testing for model performance differences."')
response_para = doc.add_paragraph()
response_para.add_run('Response 9: ').bold = True
response_para.add_run('Added Wilcoxon signed-rank test comparing model performance across 5-fold CV. Extra Trees Regressor significantly outperforms others (p < 0.05). Results added to Table 3.')

# Comment 10
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 10: ').bold = True
comment_para.add_run('"Include contrasting case study (low-HAB event)."')
response_para = doc.add_paragraph()
response_para.add_run('Response 10: ').bold = True
response_para.add_run('Added contrasting case study from February 15, 2022 (low-HAB period, NDCI < 0.1) demonstrating model capability across seasonal conditions. Added as Figure 6, Section 3.4.')

doc.add_paragraph()
doc.add_paragraph('=' * 80)

# REVIEWER 3 SECTION
doc.add_heading('RESPONSE TO REVIEWER 3 COMMENTS', level=1)

doc.add_heading('1. Summary', level=2)
doc.add_paragraph('Thank you for your detailed evaluation and recognition that our work "is able to make important contributions to early warning systems and lake management." Your comprehensive feedback has significantly improved the methodological rigor and clarity of our manuscript.')

doc.add_heading('2. Point-by-point response to Comments and Suggestions for Authors', level=2)

# Comment 1
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 1: ').bold = True
comment_para.add_run('"Line 88–91: Why IDW interpolation over kriging or spline interpolation?"')
response_para = doc.add_paragraph()
response_para.add_run('Response 1: ').bold = True
response_para.add_run('Added comprehensive justification in Section 2.2: IDW selected for computational efficiency, no prior spatial autocorrelation knowledge required, robustness to outliers, and proven success in similar remote sensing studies. Acknowledged limitations including spatial stationarity assumptions and edge effects.')

# Comment 2
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 2: ').bold = True
comment_para.add_run('"Line 100–104: SHAP methodology not detailed enough. Specify ML model used."')
response_para = doc.add_paragraph()
response_para.add_run('Response 2: ').bold = True
response_para.add_run('Added detailed Section 2.5: SHAP applied to Extra Trees Regressor using TreeExplainer for exact Shapley values. Computed for all 945 predictions, generating global feature importance and local explanations.')

# Comment 3
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 3: ').bold = True
comment_para.add_run('"Line 156–182: Hyperparameter tuning not described. Grid search used?"')
response_para = doc.add_paragraph()
response_para.add_run('Response 3: ').bold = True
response_para.add_run('Added comprehensive Section 2.4.2: GridSearchCV with 5-fold CV for all 19 models. Optimized n_estimators (50-500), max_depth (3-20), min_samples_split (2-10) for tree models; C, gamma, kernels for SVM; hidden layers, activation functions for neural networks. 48-hour optimization on HPC cluster.')

# Comment 4
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 4: ').bold = True
comment_para.add_run('"Line 178–179: Specify date ranges for temporal validation."')
response_para = doc.add_paragraph()
response_para.add_run('Response 4: ').bold = True
response_para.add_run('Specified exact ranges in Section 2.4.3: Training (June 2020 - August 2022, 795 days), Testing (September 2022 - December 2022, 150 days). 80-20 temporal split ensures testing on unseen future data.')

# Comment 5
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 5: ').bold = True
comment_para.add_run('"Line 315–323: Scalability claims need validation strategy for other lakes."')
response_para = doc.add_paragraph()
response_para.add_run('Response 5: ').bold = True
response_para.add_run('Added Section 4.4 with comprehensive validation strategy: (1) lakes with different morphology/trophic status, (2) geographic adaptation, (3) state agency collaboration, (4) multi-lake Midwest network, (5) transfer learning approaches. Acknowledged current claims are preliminary.')

# Comment 6
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 6: ').bold = True
comment_para.add_run('"Line 338–343: Include concrete follow-up activities."')
response_para = doc.add_paragraph()
response_para.add_run('Response 6: ').bold = True
response_para.add_run('Revised with specific activities: (1) Illinois EPA pilot program, (2) mobile app prototype, (3) USGS Water Quality Portal integration, (4) Great Lakes region expansion, (5) EPA CyAN collaboration. Included timelines and funding strategies.')

# Comment 7
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 7: ').bold = True
comment_para.add_run('"Line 375–385: Discuss satellite limitations (clouds, atmospheric noise)."')
response_para = doc.add_paragraph()
response_para.add_run('Response 7: ').bold = True
response_para.add_run('Added comprehensive Section 4.3: Cloud cover affects ~30% observations, handled through temporal gap-filling (≤3 days), dual Sentinel platforms, cloud masking with Scene Classification Layer, data quality flags. Atmospheric correction via Sen2Cor processor.')

# Comment 8
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 8: ').bold = True
comment_para.add_run('"Line 175: Use past tense consistently."')
response_para = doc.add_paragraph()
response_para.add_run('Response 8: ').bold = True
response_para.add_run('Corrected to: "we normalized the feature set using z-score normalization."')

# Comment 9
comment_para = doc.add_paragraph()
comment_para.add_run('Comment 9: ').bold = True
comment_para.add_run('"Line 327: Improve sentence structure."')
response_para = doc.add_paragraph()
response_para.add_run('Response 9: ').bold = True
response_para.add_run('Revised to: "Additionally, as shown in Figure 5, NDCI peaks were found..."')

# Minor corrections
doc.add_heading('Minor Grammatical Corrections:', level=3)
doc.add_paragraph('All identified grammatical errors have been corrected throughout the manuscript:')
doc.add_paragraph('• Line 6: "and also capture" → "and capture"')
doc.add_paragraph('• Line 37: Improved phrasing for marine/freshwater HAB impacts')
doc.add_paragraph('• Line 56: "taking measurements" → "measurement collection"')
doc.add_paragraph('• Line 72: "for HAB detection" → "for detecting HABs"')
doc.add_paragraph('• Line 261: Clarified "cascading effects" context')

doc.add_paragraph()
doc.add_paragraph('=' * 80)

# ADDITIONAL CLARIFICATIONS
doc.add_heading('ADDITIONAL CLARIFICATIONS', level=1)

doc.add_paragraph('1. Data Availability: All code and processed datasets will be made publicly available through GitHub and Zenodo upon publication.')
doc.add_paragraph('2. Computational Requirements: Model runs on standard hardware, accessible for operational use by agencies with limited resources.')
doc.add_paragraph('3. Real-time Implementation: Daily predictions possible within 2-3 hours of satellite data availability.')
doc.add_paragraph('4. Stakeholder Engagement: Initiated discussions with Illinois EPA and SIU Environmental Resources Training Center.')

doc.add_paragraph()
doc.add_paragraph('We believe these revisions have significantly strengthened our manuscript and addressed all reviewer concerns. We are grateful for the thorough and constructive feedback that has improved the quality and impact of our work.')

doc.add_paragraph()
doc.add_paragraph('Sincerely,')
doc.add_paragraph('Vatsal Mitesh Tailor, Anuj Tiwari, and Marcia R. Silva')

# Save the document
doc.save('HAB_Paper_Reviewer_Response_Final.docx')
print('Complete reviewer response document created successfully!')
