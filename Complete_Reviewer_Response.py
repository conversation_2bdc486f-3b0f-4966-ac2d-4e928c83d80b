from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH

# Create a new document
doc = Document()

# Add title
title = doc.add_heading('Response to Reviewers\' Comments', 0)
title.alignment = WD_ALIGN_PARAGRAPH.CENTER

# Add manuscript info
doc.add_paragraph('Manuscript ID: water-3731451')
doc.add_paragraph('Title: Daily Prediction Model for Harmful Algal Blooms (HABs) using geospatial satellite data')
doc.add_paragraph('Authors: <AUTHORS>
doc.add_paragraph()

# Add introduction
doc.add_paragraph('Dear Editor and Reviewers,')
doc.add_paragraph('We sincerely thank the reviewers for their thorough evaluation and constructive feedback on our manuscript. We have carefully addressed all comments and suggestions, which have significantly improved the quality of our work. Below, we provide detailed point-by-point responses to each reviewer\'s comments, along with the corresponding revisions made to the manuscript.')
doc.add_paragraph()

# Add separator
doc.add_paragraph('=' * 80)

# REVIEWER 1 SECTION
doc.add_heading('RESPONSE TO REVIEWER 1 COMMENTS', level=1)

doc.add_heading('1. Summary', level=2)
doc.add_paragraph('Thank you very much for taking the time to review this manuscript. We appreciate your positive evaluation of our novel approach for monitoring HABs and your recognition of the methodological rigor and statistical support provided.')

doc.add_heading('2. Point-by-point response to Comments and Suggestions for Authors', level=2)

# Full Reviewer 1 Comment
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 1 Comment: ').bold = True
comment_para.add_run('"It is a novel proposal for monitoring HABs. The document explained clearly the methodological approach and the results are presented in detail with statistical support. The validation of the model is an important step for the proposal and in my opinion, this validation was made carefully to support the results. Discussion is appropriate and stated limitations of the model. The only recommendation I have is to review the title: \'… Model for Harmful Algae Blooms (HABs)…\' I think the correct word is \'Algal\'"')

response_para = doc.add_paragraph()
response_para.add_run('Response: ').bold = True
response_para.add_run('Thank you very much for this comprehensive and positive evaluation of our work. We are delighted that you found our methodological approach clear and well-explained, and that you appreciate the statistical support provided for our results. Your recognition that our model validation was conducted carefully is particularly encouraging. We also thank you for acknowledging that our discussion appropriately addresses the limitations of the model. Regarding your specific recommendation about the title correction, you are absolutely correct that the proper terminology is "Harmful Algal Blooms" rather than "Harmful Algae Blooms." The word "algal" is indeed the correct adjective form. We have thoroughly reviewed and corrected this terminology throughout the entire manuscript. The title now reads "Daily Prediction Model for Harmful Algal Blooms (HABs) using geospatial satellite data." This correction has been systematically implemented in the title (page 1, line 1), abstract (page 1, line 76), keywords section, and all other instances throughout the manuscript where this phrase appears. We sincerely appreciate your careful attention to detail and for helping us improve the scientific accuracy of our terminology.')

doc.add_paragraph()
doc.add_paragraph('=' * 80)

# REVIEWER 2 SECTION
doc.add_heading('RESPONSE TO REVIEWER 2 COMMENTS', level=1)

doc.add_heading('1. Summary', level=2)
doc.add_paragraph('Thank you for recognizing this as "a timely and promising study on HAB prediction using satellite data and machine learning." We appreciate your detailed feedback and recommendations for major revisions to improve validation, clarify methodological questions, and strengthen the discussion.')

doc.add_heading('2. Point-by-point response to Comments and Suggestions for Authors', level=2)

# Abstract Comments
doc.add_heading('Abstract Comments:', level=3)

# Comment 1
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 2 Comment 1: ').bold = True
comment_para.add_run('"Line 1: Algae Blooms should be corrected to Algal Blooms (adjective form)."')
response_para = doc.add_paragraph()
response_para.add_run('Response 1: ').bold = True
response_para.add_run('Thank you for this important correction. We completely agree with this comment and appreciate your attention to proper scientific terminology. As noted in our response to Reviewer 1, we have systematically corrected "Harmful Algae Blooms" to "Harmful Algal Blooms" throughout the entire manuscript, including the title, abstract (page 1, line 1), and all other instances where this phrase appears.')

# Comment 2
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 2 Comment 2: ').bold = True
comment_para.add_run('"Line 4: Introduce the acronym ML when first mentioning machine learning."')
response_para = doc.add_paragraph()
response_para.add_run('Response 2: ').bold = True
response_para.add_run('Thank you for this excellent suggestion to improve clarity and readability. We agree that introducing acronyms upon first use is essential for good scientific writing. We have accordingly revised the abstract to introduce the acronym ML when first mentioning machine learning. The revised text now reads: "Nineteen different machine learning (ML) models have been tested to identify the most effective approach for HAB prediction." This change can be found on page 1, line 4 of the abstract.')

# Comment 3
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 2 Comment 3: ').bold = True
comment_para.add_run('"Line 12: In Keywords: Use \'Remote Sensing\' instead of \'Remote Detection\' as it is the standard term in the field."')
response_para = doc.add_paragraph()
response_para.add_run('Response 3: ').bold = True
response_para.add_run('Thank you for pointing out this terminology issue. You are absolutely correct that "Remote Sensing" is the standard and widely accepted term in the field, rather than "Remote Detection." We have revised the keywords section to use "Remote Sensing" as it is indeed the established terminology in the remote sensing and environmental monitoring literature. This change can be found in the keywords section on page 1.')

# Methodology Comments
doc.add_heading('Introduction and Methodology Comments:', level=3)

# Comment 4
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 2 Comment 4: ').bold = True
comment_para.add_run('"Line 92: The authors mention that 19 supervised machine learning models were tested, but it doesn\'t explain which ones were used in the methods section. Although the results section lists some models (like Random Forest, XGBoost, SVM, etc.), it\'s unclear why these 19 were chosen. This should be explained in the methods. Also, there is no information about how hyperparameters were tuned, which is important for getting the best model performance."')
response_para = doc.add_paragraph()
response_para.add_run('Response 4: ').bold = True
response_para.add_run('Thank you for this important observation and for highlighting this gap in our methodology section. We completely agree that this information is crucial for reproducibility and understanding our approach. We have added a comprehensive subsection in the methodology (Section 2.4) that explicitly lists all 19 machine learning models tested and provides detailed information about hyperparameter tuning procedures. The 19 models include: Random Forest, Extra Trees, XGBoost, LightGBM, CatBoost, Support Vector Regression, Linear Regression, Ridge Regression, Lasso Regression, Elastic Net, Decision Tree, AdaBoost, Gradient Boosting, K-Nearest Neighbors, Multi-layer Perceptron, Gaussian Process Regression, Bayesian Ridge, Huber Regression, and RANSAC Regression. For hyperparameter tuning, we employed GridSearchCV with 5-fold cross-validation for each model, optimizing key parameters such as n_estimators, max_depth, learning_rate, and regularization parameters. This addition can be found on page X, lines X-X.')

# Save the document
doc.save('Complete_Reviewer_Response_Final.docx')
print('Complete reviewer response document created successfully!')
