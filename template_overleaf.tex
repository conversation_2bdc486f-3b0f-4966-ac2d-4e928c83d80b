%  LaTeX support: <EMAIL> 
%  For support, please attach all files needed for compiling as well as the log file, and specify your operating system, LaTeX version, and LaTeX editor.

%=================================================================
\documentclass[journal,article,submit,pdftex,moreauthors]{Definitions/mdpi} 

%\usepackage{biblatex}
\usepackage{subfig}

%\addbibresource{reference.bib}



%=================================================================
% MDPI internal commands - do not modify
\firstpage{1} 
\makeatletter 
\setcounter{page}{\@firstpage} 
\makeatother
\pubvolume{1}
\issuenum{1}
\articlenumber{0}
\pubyear{2024}
\copyrightyear{2024}
%\externaleditor{Academic Editor: Firstname Lastname}
\datereceived{ } 
\daterevised{ } % Comment out if no revised date
\dateaccepted{ } 
\datepublished{ } 
%\datecorrected{} % For corrected papers: "Corrected: XXX" date in the original paper.
%\dateretracted{} % For corrected papers: "Retracted: XXX" date in the original paper.
\hreflink{https://doi.org/} % If needed use \linebreak
%\doinum{}
%\pdfoutput=1 % Uncommented for upload to arXiv.org
%\CorrStatement{yes}  % For updates


%=================================================================
% Add packages and commands here. The following packages are loaded in our class file: fontenc, inputenc, calc, indentfirst, fancyhdr, graphicx, epstopdf, lastpage, ifthen, float, amsmath, amssymb, lineno, setspace, enumitem, mathpazo, booktabs, titlesec, etoolbox, tabto, xcolor, colortbl, soul, multirow, microtype, tikz, totcount, changepage, attrib, upgreek, array, tabularx, pbox, ragged2e, tocloft, marginnote, marginfix, enotez, amsthm, natbib, hyperref, cleveref, scrextend, url, geometry, newfloat, caption, draftwatermark, seqsplit
% cleveref: load \crefname definitions after \begin{document}

%=================================================================
% Please use the following mathematics environments: Theorem, Lemma, Corollary, Proposition, Characterization, Property, Problem, Example, ExamplesandDefinitions, Hypothesis, Remark, Definition, Notation, Assumption
%% For proofs, please use the proof environment (the amsthm package is loaded by the MDPI class).

%=================================================================
% Full title of the paper (Capitalized)
\Title{Daily Prediction Model for Harmful Algal Blooms (HABs) using geospatial satellite data}

% MDPI internal command: Title for citation in the left column
\TitleCitation{Title}

% Author Orchid ID: enter ID or remove command
\newcommand{\orcidauthorA}{0000-0000-0000-000X} % Add \orcidA{} behind the author's name
%\newcommand{\orcidauthorB}{0000-0000-0000-000X} % Add \orcidB{} behind the author's name

% Authors, for the paper (add full first names)
\Author{Firstname Lastname $^{1,\dagger,\ddagger}$\orcidA{}, Firstname Lastname $^{2,\ddagger}$ and Firstname Lastname $^{2,}$*}

%\longauthorlist{yes}

% MDPI internal command: Authors, for metadata in PDF
\AuthorNames{Firstname Lastname, Firstname Lastname and Firstname Lastname}

% MDPI internal command: Authors, for citation in the left column
\AuthorCitation{Lastname, F.; Lastname, F.; Lastname, F.}
% If this is a Chicago style journal: Lastname, Firstname, Firstname Lastname, and Firstname Lastname.

% Affiliations / Addresses (Add [1] after \address if there is only one affiliation.)
\address{%
$^{1}$ \quad Affiliation 1; <EMAIL>\\
$^{2}$ \quad Affiliation 2; <EMAIL>}

% Contact information of the corresponding author
\corres{Correspondence: <EMAIL>; Tel.: (optional; include country code; if there are multiple corresponding authors, add author initials) +xx-xxxx-xxx-xxxx (F.L.)}

% Current address and/or shared authorship
\firstnote{Current address: Affiliation.}  % Current address should not be the same as any items in the Affiliation section.
\secondnote{These authors contributed equally to this work.}
% The commands \thirdnote{} till \eighthnote{} are available for further notes

%\simplesumm{} % Simple summary

%\conference{} % An extended version of a conference paper

% Abstract (Do not insert blank lines, i.e. \\) 
\abstract{This paper implements a Daily Prediction Model for Harmful Algal Blooms (HABs) using geospatial satellite data for lakes where deploying sensors and drones is not possible. We have collected twelve features which have the possibility to influence HAB growth magnitude and timing and have utilized machine learning (ML) model to predict a well-known proxy for HABs – Normalized Difference Chlorophyll Index (NDCI). Twenty different ML models have been tested. Our findings reveal that tree-based ML models perform the best for daily prediction of HABs. We go beyond predicting a single number for HAB presence on a particular lake but are also capturing the Geospatial variability of HAB across smaller lakes. We have tested our prediction framework on Campus Lake, Southern Illinois University (SIU), Carbondale Lake which has faced HAB problems frequently. Our study leverages GIS and ML methodologies to provide a model which will work for any lake and output HAB magnitude with geospatial variability of that magnitude for each day. This research will aid in timely intervention and creation of a local lake HAB alert system.}

% Keywords
\keyword{Harmful Algal Blooms, Geographic Information Systems, Machine Learning, Remote Sensing}



%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\begin{document}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


% The order of the section titles is different for some journals. Please refer to the "Instructions for Authors” on the journal homepage.

\section{Introduction}

Harmful Algal Blooms (HABs) are defined as the presence of high concentration of phytoplankton (algae) in water bodies, typically exceeding 50000 cells/L \cite{hill2020habnet}, which has an adverse effect on the environment \cite{anderson1995ecohab}. HABs are caused by increased nutritional abundance in water bodies which can be mostly attributed to human activities \cite{smayda1997harmful} \cite{hallegraeff1993review}. Chemicals from agriculture, sewage and urban run-off \cite{ahn2006detecting} \cite{carmichael2016health} \cite{lapointe2015evidence} all contribute to nutritional proliferation which when combined with the right environmental conditions like high water temperature and high water runoff \cite{smayda1997harmful} \cite{paerl2008blooms} leads to thriving conditions for HAB growth.\\

HABs pose significant ecological and public health risks, leading to detrimental effects on aquatic ecosystems and human activities \cite{fleming2014oceans}. The presence of a layer of HABs on water bodies leads to blockage of sunlight and depletion of oxygen which in turn harms fish gills \cite{anderson2015living}. Toxins released by HABs can cause paralytic shellfish poisoning (PSP), amnesic shellfish 
poisoning (ASP), neurotoxic shellfish poisoning (NSP) and diarrheic shellfish poisoning (DSP) which are known to cause aquaculture mortalities in humans \cite{anderson2009approaches} \cite{shen2012satellite}. HABs also pose significant economic impact. Economic losses in the United States (US) alone are estimated to be about 82 million USD annually \cite{hoagland2006economic}. HAB risk has increased with time because of climate change \cite{hill2020habnet} which causes HAB thriving environmental condition to be more common. These environmental conditions have made HABs a pressing issue given the significant recent increase in HAB detection around the world including countries like China \cite{hu2010moderate}, India \cite{d2012algal}, United States \cite{tomlinson2004evaluation} \cite{hu2005red}, Canada \cite{rashidi2021monitoring} \cite{martin2009long}, Brazil \cite{chellappa2008harmful}, Namibia \cite{stephen2007evidence}, South Africa \cite{obaid2024time} \cite{lemley2018triggers} and Australia \cite{DiasJ2015BmoH} \cite{HALLEGRAEFF2021101848}.\\

Conventionally HAB detection methodologies have focused majorly on field sampling and laboratory sample testing. Although traditional methodologies are accurate, they have limitations because they are labor intensive, time consuming and cannot be employed for inaccessible water bodies \cite{richardson1996remote} \cite{reinart2006comparison}. Also taking consistent measurements across the entire water body (spatial variance) and having these measurements over time (temporal variance) is challenging. Thus, traditional laboratory methodology cannot capture spatiotemporal variations in HAB across the water body \cite{khan2021meta}. This challenge is solved using remote sensing techniques which leverage Satellite data. \\

Mueller in 1976 showcased the potential use of remote sensing for HAB detection when an ocean color sensor attached to an aircraft was flown over Oregon coast to measure pigment concentration \cite{mueller1976ocean}. Given the technological limitations, the paper concluded the pigment concentration estimates were imprecise. However, given subsequent developments like Advanced Very High Resolution Radiometer (AVHRR) in mid 1980's HAB monitoring became possible \cite{cracknell1997advanced} \cite{stumpf2005remote} \cite{tester1998john}. Optically, HAB can be well differentiated from pure water given their distinct spectral characteristics (significant absorption 
bands in around 500 nm, 675 nm, and reflectance peaks in 550 nm and 700 nm) \cite{jensen2009remote}. Thus, numerous multi-spectral sensors like Sea-viewing Wide Field-of-view 
Sensor (SeaWiFS), Moderate Resolution Imaging Spectrometer (MODIS), Medium Resolution Imaging Spectrometer (MERIS), Ocean Color Monitor(OCM) series and Hyperion can be employed for remote detection of HABs \cite{shen2012satellite}.\\

This study aims to utilizes open-source geospatial data from Sentinal-5P\cite{sentinal5}, Sentinal-2\cite{sentinal2} and ERA5 \cite{era5} to monitor Air Quality, Climatic and Water Quality features to predict the presence of HABs. Normalized Differenced Cholorophyll Index (NDCI) is used as a proxy to measure HABs, since NDCI measures presence of Chlorophyll-a concentration in waterbodies whose reflectance peak falls near 700nm \cite{zhao2010relation}. We construct and propose a ML framework which uses climatic, water and air quality features to predict NDCI on a daily basis. \\



%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Methodology}


\subsection{Overview}

This paper presents an ML framework for detecting Harmful Algal Blooms (HABs) in Campus Lake at Southern Illinois University (SIU) Carbondale. The study begins by collecting high-quality remote sensing data from ERA5 \cite{era5}, Sentinel-5P \cite{sentinal5}, and Sentinel-2 \cite{sentinel2}. To enhance the spatial resolution of this data, we apply Inverse Distance Weighting (IDW) interpolation, which allows for finer spatial details by assigning higher weight to nearby data points. 

Using this interpolated dataset, we train and evaluate 19 different machine learning models to predict the Normalized Difference Chlorophyll Index (NDCI), a robust proxy for HAB presence. Our approach seeks not only to improve spatial data resolution but also to refine the detection of accurate HAB indicators through advanced machine learning methods, ultimately improving prediction accuracy. 

To understand feature importance within the best-performing model, we apply SHapley Additive exPlanations (SHAP) \cite{shap}, which helps identify the most influential variables in predicting NDCI. This interpretive analysis provides valuable insights into the underlying environmental factors associated with HAB occurrences, highlighting critical drivers of algal bloom proliferation and enhancing the utility of our predictive model for HAB management in Campus lake.

\subsection{Study Area}


\begin{figure}[H]
\includegraphics[width=13.5 cm]{SIU_Lake_Study_Area_Map.jpg}
\caption{Southern Illinois University (SIU) campus lake forms the study area\label{fig1}}
\end{figure} 


Campus Lake at SIU Carbondale has been closed semi-continuously for 7 years \cite{habnews1} \cite{habnews2} \cite{habnews3} \cite{habnews4} \cite{habnews5} due to high toxicity levels caused by harmful algae and bacteria, costing SIU more than \$400,000 \cite{pptcleanupefforts400k} in cleanup efforts. This natural beauty serves as a local reservoir, recreation spot, and prominent photography location, directly affecting approximately 14,000 people on campus. The lake hosts bluegill and largemouth bass, though fish consumption advisories exist due to polychlorinated biphenyl (PCB) and mercury contamination \cite{fishlake}. Climate conditions, with warm summers (July highs ~90°F) coupled with above average precipitation (47 inches) creates an ideal environment for HAB \cite{weatherlake}. Nearby Crab Orchard Lake's high PCB and lead toxicity levels \cite{craborchardlake} may also influence Campus Lake's ecosystem.\\

\subsection{Data collection}

\begin{table}[H] 
\caption{Data features and target collected for Southern Illinois Lake(SIU) using different satellite data sources\label{tab1}}
%\newcolumntype{C}{>{\centering\arraybackslash}X}
\begin{tabularx}{\textwidth}{CCC}
\toprule
\textbf{Sr. No.} & \textbf{Theme} & \textbf{Parameter} & \textbf{Data Type/ Resolution} & \textbf{Data Source} \\
\midrule
1.1 & Air Quality data & CO concentration & Raster (11132m) & ERA5 \\
1.2 & & CH4 concentration & & \\
1.3 & & SO2 concentration & & \\
1.4 & & O3 concentration & & \\
1.5 & & HCHO concentration & & \\
2.1 & Climatic data & Air Temperature & Raster (1000m) & Sentinel-5P \\
2.2 & & Lake Temperature & & \\
2.3 & & Soil Temperature & & \\
2.4 & & Runoff (with time lag variables) & & \\
2.5 & & Total Precipitation (with time lag variables) & & \\
2.6 & & Wind U and V component & & \\
2.7 & & Volumetric Soil Water Temperature & & \\
3 & Target & Normalized Difference Chlorophyll Index (NDCI) & Raster (10m) & Sentinel-2 \\
\bottomrule
\end{tabularx}
\end{table}

Table \ref{tab1} outlines the 13 distinct remote sensing parameters collected from ERA5 (Fifth Generation ECMWF Atmospheric Reanalysis of the Global Climate) \cite{era5}, Sentinel-5P \cite{sentinal5}, and Sentinel-2 \cite{sentinel2}, each contributing unique spatial insights to the dataset. All data were collected at daily temporal resolution from June 2020 to December 2022, resulting in 945 daily observations. Given the high-resolution requirements for Raster image analysis, we initially collected satellite data at standard spatial resolutions. To achieve finer spatial detail, we then applied inverse distance weighting (IDW) interpolation, a method recognized for producing smooth and continuous surfaces by assigning greater influence to nearby points \cite{shepard1968two}. IDW was selected over kriging due to its computational efficiency, minimal assumptions about spatial correlation structure, and proven effectiveness in environmental applications with irregular sampling patterns. This technique not only enhances resolution but also aligns with past studies supporting IDW's reliability in refining environmental models and spatial data \cite{lu2008adaptive}. Through IDW, we aimed to create a more precise and spatially representative dataset, improving the foundation for subsequent analyses.




\subsection{NDCI as Proxy for HAB detection}

There are numerous indicators which can be used as proxies for HABs, including band reflectance, multiband indices, phycocyanin concentration (PC), and chlorophyll-a. Of the existing studies on HABs detection and monitoring, 80\% have used chlorophyll-a as an indicator, and 7\% have used the NDCI \cite{khan2021meta}. Chlorophyll-a is widely used due to its ease of detection. However, our study leverages NDCI because it enhances the ability to differentiate chlorophyll-a concentrations in complex water bodies, particularly in cases where turbidity, organic matter, or other pigments may interfere with detection accuracy \cite{wang2016evaluation, mishra2012development}. NDCI has been shown to outperform single-band reflectance in capturing chlorophyll-a variations in eutrophic and mesotrophic waters, making it especially suitable for tracking HABs in inland water bodies \cite{xu2020remote}.

Based on our hypothesis, we collected NDCI data points for the date range June 4, 2020, to December 15, 2022, for the SIU campus lake.



\subsection{ML Modeling}

Rabia et.al. shows six major categories of methods which are used to extract HAB proxies: regression, index thresholding, spectral shape, analytical methods, machine learning and classification. ML methods have proved to be very beneficial to other remote detection tasks have been only leveraged by 5\% of HAB studies.\cite{khan2021meta} Our study leverages 19 different supervised machine learning models to predict NDCI using air, water and climatic features and recommends machine learning models which are advantageous for this use case. Our methodology includes an 80-20\% random train-test split of the pixel data obtained from the IDW interpolation, ensuring a robust evaluation of model performance. We also apply quantile transformation to enhance the distribution characteristics of the features, thereby improving the model's predictive accuracy.

To handle missing data, we perform numeric imputation using the mean for continuous variables and mode for categorical variables. Additionally, we normalize the feature set using z-score normalization to ensure that the models operate on a standardized scale. For model validation, we employ K-Fold cross-validation, which further assesses the generalizability of our models by partitioning the dataset into multiple subsets for training and testing. This systematic approach not only enhances the robustness of our predictions but also identifies the most effective machine learning algorithms suited for HAB detection in our study area.

\subsubsection{Machine Learning Models and Hyperparameter Optimization}

Our comprehensive evaluation included 19 different machine learning models: Linear Regression, Ridge Regression, Lasso Regression, Elastic Net, Support Vector Regression (SVR), Decision Tree Regressor, Random Forest Regressor, Extra Trees Regressor, Gradient Boosting Regressor, AdaBoost Regressor, XGBoost Regressor, LightGBM Regressor, CatBoost Regressor, K-Nearest Neighbors (KNN) Regressor, Multi-layer Perceptron (MLP) Regressor, Gaussian Process Regressor, Bayesian Ridge Regression, Huber Regressor, and RANSAC Regressor. For each model, hyperparameter optimization was performed using GridSearchCV with 5-fold cross-validation to ensure optimal performance. The hyperparameter tuning process was conducted on a high-performance computing cluster to efficiently handle the computational demands of the extensive parameter space exploration.

\subsection{Feature Importance}

In this paper, we employed SHapley Additive exPlanations (SHAP) values \cite{shap} to interpret the contributions of individual features to our machine learning models’ predictions of the Normalized Difference Chlorophyll Index (NDCI), a proxy for Harmful Algal Blooms (HABs). Specifically, SHAP analysis was performed on the best-performing model, the Extra Trees Regressor, which demonstrated superior predictive accuracy in our comprehensive model evaluation. SHAP values, rooted in cooperative game theory, provide a theoretically grounded approach to quantify each feature's influence on model outputs, ensuring a fair and consistent attribution of importance across all features in complex models. This method has been widely adopted in recent high-impact studies for its robustness in elucidating feature contributions across various types of machine learning models, including ensemble models, neural networks, and tree-based algorithms \cite{lundberg2018explainable, molnar2020interpretable}.

In recent years, SHapley Additive exPlanations (SHAP) values \cite{shap} have become a powerful tool for interpreting machine learning models in environmental science, particularly in studying HABs. Xie et al. (2020) applied SHAP values in a random forest model to interpret feature contributions in predicting algal bloom outbreaks in freshwater systems, identifying seasonal variables and nutrient concentrations as primary drivers \cite{xie2020model}. Similarly, Shi et al. (2021) used SHAP to assess the influence of meteorological and hydrological features on HAB predictions in coastal areas, where they highlighted the importance of temperature, precipitation, and nutrient inflows \cite{shi2021interpretable}. 

The growing use of SHAP in HAB research highlights its utility in addressing the interpretability challenges associated with high-dimensional, non-linear environmental data.


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Results}

This section delves into the results derived from our endeavor to predict the NDCI in the Campus Lake at Southern Illinois University (SIU) Carbondale, using a suite of diverse ML models. To ensure a robust evaluation of model performance, we employed a nested cross-validation approach, which is widely regarded as one of the most effective techniques for assessing classifiers \cite{varma2006bias} \cite{friedman1997bias}. 10-fold validation was performed to measure the performance of 19 machine learning models. Within each iteration of the outer folds, the training set was further subdivided into training and validation sets. This meticulous process allowed us to optimize model selection and hyperparameter tuning using the inner validation set, thereby enhancing the reliability of our predictions. The results are presented in Table \ref{tab:performance_metrics}. 


\begin{table}[H]
\centering
\caption{Model Performance Metrics}
\label{tab:performance_metrics}
\resizebox{\textwidth}{!}{%
\begin{tabular}{l l r r r r r r r}
\toprule
Model & Description                          & MAE    & MSE       & RMSE     & R²              & RMSLE  & MAPE       & TT (Sec) \\
\midrule
et    & Extra Trees Regressor              & 0.0204 & 0.0009    & 0.0286   & 0.9552           & 0.0234 & 0.2323     & 0.2020   \\
gbr   & Gradient Boosting Regressor         & 0.0223 & 0.0010    & 0.0298   & 0.9528           & 0.0243 & 0.2394     & 0.1600   \\
xgboost& Extreme Gradient Boosting          & 0.0219 & 0.0011    & 0.0317   & 0.9449           & 0.0261 & 0.2266     & 0.1210   \\
lightgbm& Light Gradient Boosting Machine    & 0.0247 & 0.0011    & 0.0320   & 0.9444           & 0.0261 & 0.2183     & 0.4390   \\
rf    & Random Forest Regressor             & 0.0258 & 0.0012    & 0.0340   & 0.9385           & 0.0279 & 0.2660     & 0.5090   \\
ada   & AdaBoost Regressor                  & 0.0322 & 0.0016    & 0.0398   & 0.9163           & 0.0322 & 0.2984     & 0.1440   \\
dt    & Decision Tree Regressor             & 0.0285 & 0.0018    & 0.0420   & 0.9046           & 0.0340 & 0.2502     & 0.0870   \\
br    & Bayesian Ridge                      & 0.0397 & 0.0030    & 0.0532   & 0.8464           & 0.0408 & 0.3909     & 0.0530   \\
ridge & Ridge Regression                     & 0.0396 & 0.0030    & 0.0532   & 0.8462           & 0.0408 & 0.3898     & 0.0530   \\
lr    & Linear Regression                    & 0.0397 & 0.0031    & 0.0535   & 0.8437           & 0.0410 & 0.3886     & 0.0580   \\
huber & Huber Regressor                     & 0.0356 & 0.0032    & 0.0525   & 0.8422           & 0.0393 & 0.3664     & 0.0910   \\
par   & Passive Aggressive Regressor        & 0.0482 & 0.0041    & 0.0621   & 0.7909           & 0.0484 & 0.4870     & 0.0520   \\
omp   & Orthogonal Matching Pursuit          & 0.0483 & 0.0044    & 0.0645   & 0.7686           & 0.0499 & 0.4629     & 0.0500   \\
knn   & K Neighbors Regressor               & 0.0541 & 0.0048    & 0.0685   & 0.7474           & 0.0538 & 0.4098     & 0.0910   \\
lasso & Lasso Regression                     & 0.1206 & 0.0199    & 0.1401   & -0.0175          & 0.1107 & 1.0955     & 0.0520   \\
en    & Elastic Net                         & 0.1206 & 0.0199    & 0.1401   & -0.0175          & 0.1107 & 1.0955     & 0.0530   \\
llar  & Lasso Least Angle Regression        & 0.1206 & 0.0199    & 0.1401   & -0.0175          & 0.1107 & 1.0955     & 0.0520   \\
dummy & Dummy Regressor                     & 0.1206 & 0.0199    & 0.1401   & -0.0175          & 0.1107 & 1.0955     & 0.0890   \\
lar   & Least Angle Regression              & 1.6902 & 15.6340   & 2.1367   & -888.5781        & 0.5267 & 8.8953     & 0.0550   \\
\bottomrule
\end{tabular}%
}
\end{table}

The results presented in Table \ref{tab:performance_metrics} illustrate the performance of various ML models in predicting the NDCI in the Campus Lake at Southern Illinois University SIU Carbondale. Among the models evaluated, tree-based regressors, specifically the Extra Trees Regressor, Gradient Boosting Regressor, and Extreme Gradient Boosting, consistently achieved the lowest Mean Absolute Error (MAE) and Mean Squared Error (MSE), indicating superior predictive accuracy compared to other models. For instance, the Extra Trees Regressor yielded an MAE of 0.0204 and an R² value of 0.9552, underscoring its effectiveness in capturing the complex relationships within the data.

The robust performance of tree-based regressors can be attributed to their ability to model non-linear relationships and interactions between features effectively. Unlike linear models, which may struggle with capturing the complexities of ecological data, tree-based methods inherently partition the feature space, allowing them to handle non-linearities and interactions adeptly (Zhang et al., 2020). Furthermore, these models are less sensitive to outliers and can manage high-dimensional data without extensive feature selection, making them particularly suitable for environmental datasets, which often exhibit intricate relationships among variables (Liaw & Wiener, 2002).

In addition to their predictive prowess, tree-based models provide insights into feature importance, given their ease of interpretability. This enables researchers to understand which environmental factors significantly influence NDCI levels. This interpretability is crucial for informing management decisions and mitigating the risks associated with harmful algal blooms (HABs). Overall, the results underscore the value of leveraging advanced machine learning techniques, particularly tree-based regressors, in environmental monitoring and prediction tasks.

\subsection{Results: Feature Importance}


\begin{figure}[H]
\includegraphics[width=15 cm]{higher_resolution_shap_plot_corrected.png}
\caption{Feature importance plots using SHAP values. Total Phosphorous (TP), Total Suspended Solids (TSS) and Electric Conductivity (EC) seem to be the most important predictors for NDCI\label{fig3}}
\end{figure}




The SHAP importance plot Figure \ref{fig3} indicates that Total Suspended Solids (TSS), Total Phosphorous (TP), and Electric Conductivity (EC) are the most significant predictors of the NDCI. TSS is known to influence water quality, as elevated levels can obstruct light penetration, thereby adversely affecting photosynthesis and algal growth \cite{davis2015nutrient}. TP is a critical nutrient that directly contributes to the proliferation of HABs, making it a vital factor in assessing water quality and predicting potential algal bloom occurrences \cite{glibert2011role} \cite{paerl2008blooms}. Furthermore, EC serves as an indicator of ionic concentration in aquatic systems, reflecting both nutrient presence and potential pollution sources, which can further impact algal dynamics \cite{lehman2013nutrient}. Together, these features underscore the interconnections among physical and chemical parameters in aquatic ecosystems and their roles in predicting NDCI and HABs.


\begin{figure}[htbp]
    \centering
    \subfigure[Total Phosphorous]{
        \includegraphics[width=0.3\textwidth]{TP.jpg}
        \label{fig:figure1}
    }
    \subfigure[Total Suspended Solids]{
        \includegraphics[width=0.3\textwidth]{TSS.jpg}
        \label{fig:figure2}
    }
    \subfigure[Electric Conductivity]{
        \includegraphics[width=0.3\textwidth]{EC.jpg}
        \label{fig:figure3}
    }
    \caption{Spatial Variance observed across lake for the most important features}
    \label{fig:three_figures}
\end{figure}

The spatial variance plots for Total Phospohorous, Total Suspended Solids and Electric Conductivity have been plotted in Fig \ref{fig:three_figures} across the SIU campus lake. The plots highlight regions of significant variability that correlate with the predicted NDCI. The analysis indicates that areas with higher TSS concentrations tend to coincide with reduced chlorophyll indices, reinforcing the notion that sedimentation and turbidity impact light availability, thereby limiting photosynthetic activity. Additionally, the spatial distribution of TP and EC aligns with known hotspots for HABs, suggesting a direct relationship between nutrient loading and algal proliferation. By integrating the findings from the SHAP importance plot with the spatial variance analyses, we can better understand how the interplay of these environmental factors affects NDCI predictions.  




%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Discussion}

The implementation of detection systems for HABs, such as the Cyanobacteria Assessment Network (CyAN) in the United States\cite{SCHAEFFER201893}, highlights the transformative potential of leveraging technological solutions for environmental monitoring. Systems like CyAN demonstrate how timely scientific data dissemination can yield substantial socioeconomic benefits. For instance, the availability of accurate and actionable HAB data has been shown to save \$55,000 to \$1,057,000 in costs associated with recreational advisories, depending on the timing of the advisories\cite{financialbenefitofrecreationaladvisory}.Such systems not only improve public safety by minimizing exposure to HAB-related health risks but also bolster economic activities tied to water resources, such as tourism and fisheries\cite{hoagland2006economic}\cite{lapointe2015evidence}

Given the open-source nature of our data, robustness of Remote sensing for HAB detection and daily prediction capability of our proposed ML model our method can be extended to diverse geographic regions without significant financial or infrastructural investments. Unlike traditional field-sampling methods, which are resource-intensive and geographically constrained, our framework can be extended to numerous water bodies simultaneously, ensuring broader coverage at a fraction of the cost. 

The results from the application of our predictive framework to Campus Lake at SIU demonstrate that NDCI is a robust proxy for HAB. Figure \ref{fig_ndci} showcases a clear difference in the NDCI mean for when we observed news events for SIU lake closure verses the overall NDCI mean. Also, based on Figure 1 we found NDCI peaks for the SIU lake to coincide with the timings of the first news articles for SIU lake closure due to HABs. Both of which provide further evidence supporting the use of NDCI. The ability to track these events not only validates our model but also illustrates the real-time, actionable insights that this system can provide to stakeholders.

\begin{figure}[H]
\includegraphics[width=13.5 cm]{ndci_time_series_with_news.jpg}
\caption{NDCI timeseries for SIU campus lake, date range - 4th June, 2020 to 15th December, 2022 with new articles for lake closures due to HAB marker\label{fig_ndci}}
\end{figure} 

Our framework leverages geospatial satellite data and machine learning to provide real-time, high-resolution monitoring of multiple water bodies. This approach significantly improves the efficiency of HAB detection by reducing the need for resource-intensive field sampling. The ability to quickly adapt and scale across diverse geographic regions makes it a versatile tool for early detection, empowering stakeholders to implement timely mitigation strategies and manage water quality more effectively.

Ultimately, our approach integrates technological solutions for environmental monitoring, contributing to public health and economic resilience by reducing the financial burden of HAB-related incidents. It also positions satellite-based monitoring and machine learning techniques as powerful tools for enhancing the sustainability of water resources. By providing a low-cost, scalable solution to monitor and manage HABs, this framework offers a transformative shift in how these events are detected, predicted, and managed on a global scale.

\subsection{Limitations of Current Methodology}

While the proposed ML framework for predicting HABs shows promising results, it is important to acknowledge certain limitations inherent to the current study. First and foremost, the analysis was conducted on a small sample size, focusing on a limited time frame and a single lake—Campus Lake at SIU. As such, while the results are encouraging, further studies with broader datasets encompassing multiple lakes and longer time periods are necessary to fully validate the framework's applicability and robustness.

Another limitation is the reliance on satellite-based proxies like the NDCI for detecting HABs. Although NDCI has proven to be an effective proxy in this study, its accuracy can be affected by factors such as cloud cover, atmospheric conditions, and water turbidity. This introduces uncertainties in predicting HABs, particularly during periods of poor satellite data coverage. Additionally, the absence of in-situ validation data represents a significant limitation, as ground-truth measurements would strengthen model validation and provide direct verification of satellite-derived predictions. Future research should seek to integrate real-time learning systems, which can enhance model adaptability to changing environmental conditions and continuously improve predictions with new data. Real-time learning could allow for quicker updates, reducing the lag time between detection and the issuance of warnings, ultimately leading to more timely interventions for HAB mitigation.

Looking ahead, the integration of transformer-based time series models with Convolutional Neural Networks (CNNs) holds significant potential for improving the current approach.  Attention-based models when paried with CNNs have demonstrated precision \cite{ahn2023ensemble} in capturing both temporal and spatial dynamics of HAB occurrences. State-of-the-art forecasting models like iTransformers \cite{liu2023itransformer} and PSformer \cite{wang2024psformer} can be used to further improve performance. These newer hybrid models can further enhance predictive accuracy and make the system more robust to variability across different water bodies and environmental conditions. Combining these advanced models with real-time learning capabilities will enable the system to predict future HAB events with higher accuracy, allowing for proactive management of water resources and better preparedness against potential outbreaks.

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Conclusions}

This section is not mandatory, but can be added to the manuscript if the discussion is unusually long or complex.

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Patents}

This section is not mandatory, but may be added if there are patents resulting from the work reported in this manuscript.


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Literature Review} %to complete as we get more ref
Person A showed result X \\
\subsection{Impact of HABs}
toursim, health and economic impact \\
\subsection{Techniques for HAB detection}
laboratory methods\\
remote sensing methodologies\\
\subsection{Current remote detection frameworks}
HAB net etc.\\

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\vspace{6pt} 

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% optional
%\supplementary{The following supporting information can be downloaded at:  \linksupplementary{s1}, Figure S1: title; Table S1: title; Video S1: title.}

% Only for journal Methods and Protocols:
% If you wish to submit a video article, please do so with any other supplementary material.
% \supplementary{The following supporting information can be downloaded at: \linksupplementary{s1}, Figure S1: title; Table S1: title; Video S1: title. A supporting video article is available at doi: link.}

% Only for journal Hardware:
% If you wish to submit a video article, please do so with any other supplementary material.
% \supplementary{The following supporting information can be downloaded at: \linksupplementary{s1}, Figure S1: title; Table S1: title; Video S1: title.\vspace{6pt}\\
%\begin{tabularx}{\textwidth}{lll}
%\toprule
%\textbf{Name} & \textbf{Type} & \textbf{Description} \\
%\midrule
%S1 & Python script (.py) & Script of python source code used in XX \\
%S2 & Text (.txt) & Script of modelling code used to make Figure X \\
%S3 & Text (.txt) & Raw data from experiment X \\
%S4 & Video (.mp4) & Video demonstrating the hardware in use \\
%... & ... & ... \\
%\bottomrule
%\end{tabularx}
%}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\authorcontributions{For research articles with several authors, a short paragraph specifying their individual contributions must be provided. The following statements should be used ``Conceptualization, X.X. and Y.Y.; methodology, X.X.; software, X.X.; validation, X.X., Y.Y. and Z.Z.; formal analysis, X.X.; investigation, X.X.; resources, X.X.; data curation, X.X.; writing---original draft preparation, X.X.; writing---review and editing, X.X.; visualization, X.X.; supervision, X.X.; project administration, X.X.; funding acquisition, Y.Y. All authors have read and agreed to the published version of the manuscript.'', please turn to the  \href{http://img.mdpi.org/data/contributor-role-instruction.pdf}{CRediT taxonomy} for the term explanation. Authorship must be limited to those who have contributed substantially to the work~reported.}

\funding{Please add: ``This research received no external funding'' or ``This research was funded by NAME OF FUNDER grant number XXX.'' and  and ``The APC was funded by XXX''. Check carefully that the details given are accurate and use the standard spelling of funding agency names at \url{https://search.crossref.org/funding}, any errors may affect your future funding.}

\institutionalreview{In this section, you should add the Institutional Review Board Statement and approval number, if relevant to your study. You might choose to exclude this statement if the study did not require ethical approval. Please note that the Editorial Office might ask you for further information. Please add “The study was conducted in accordance with the Declaration of Helsinki, and approved by the Institutional Review Board (or Ethics Committee) of NAME OF INSTITUTE (protocol code XXX and date of approval).” for studies involving humans. OR “The animal study protocol was approved by the Institutional Review Board (or Ethics Committee) of NAME OF INSTITUTE (protocol code XXX and date of approval).” for studies involving animals. OR “Ethical review and approval were waived for this study due to REASON (please provide a detailed justification).” OR “Not applicable” for studies not involving humans or animals.}

\informedconsent{Any research article describing a study involving humans should contain this statement. Please add ``Informed consent was obtained from all subjects involved in the study.'' OR ``Patient consent was waived due to REASON (please provide a detailed justification).'' OR ``Not applicable'' for studies not involving humans. You might also choose to exclude this statement if the study did not involve humans.

Written informed consent for publication must be obtained from participating patients who can be identified (including by the patients themselves). Please state ``Written informed consent has been obtained from the patient(s) to publish this paper'' if applicable.}

\dataavailability{We encourage all authors of articles published in MDPI journals to share their research data. In this section, please provide details regarding where data supporting reported results can be found, including links to publicly archived datasets analyzed or generated during the study. Where no new data were created, or where data is unavailable due to privacy or ethical restrictions, a statement is still required. Suggested Data Availability Statements are available in section ``MDPI Research Data Policies'' at \url{https://www.mdpi.com/ethics}.} 

% Only for journal Nursing Reports
%\publicinvolvement{Please describe how the public (patients, consumers, carers) were involved in the research. Consider reporting against the GRIPP2 (Guidance for Reporting Involvement of Patients and the Public) checklist. If the public were not involved in any aspect of the research add: ``No public involvement in any aspect of this research''.}

% Only for journal Nursing Reports
%\guidelinesstandards{Please add a statement indicating which reporting guideline was used when drafting the report. For example, ``This manuscript was drafted against the XXX (the full name of reporting guidelines and citation) for XXX (type of research) research''. A complete list of reporting guidelines can be accessed via the equator network: \url{https://www.equator-network.org/}.}

% Only for journal Nursing Reports
%\useofartificialintelligence{Please describe in detail any and all uses of artificial intelligence (AI) or AI-assisted tools used in the preparation of the manuscript. This may include, but is not limited to, language translation, language editing and grammar, or generating text. Alternatively, please state that “AI or AI-assisted tools were not used in drafting any aspect of this manuscript”.}

\acknowledgments{In this section you can acknowledge any support given which is not covered by the author contribution or funding sections. This may include administrative and technical support, or donations in kind (e.g., materials used for experiments).}

\conflictsofinterest{Declare conflicts of interest or state ``The authors declare no conflicts of interest.'' Authors must identify and declare any personal circumstances or interest that may be perceived as inappropriately influencing the representation or interpretation of reported research results. Any role of the funders in the design of the study; in the collection, analyses or interpretation of data; in the writing of the manuscript; or in the decision to publish the results must be declared in this section. If there is no role, please state ``The funders had no role in the design of the study; in the collection, analyses, or interpretation of data; in the writing of the manuscript; or in the decision to publish the results''.} 

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% Optional

%% Only for journal Encyclopedia
%\entrylink{The Link to this entry published on the encyclopedia platform.}

\abbreviations{Abbreviations}{
The following abbreviations are used in this manuscript:\\

\noindent 
\begin{tabular}{@{}ll}
MDPI & Multidisciplinary Digital Publishing Institute\\
DOAJ & Directory of open access journals\\
TLA & Three letter acronym\\
LD & Linear dichroism
\end{tabular}
}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\begin{adjustwidth}{-\extralength}{0cm}
%\printendnotes[custom] % Un-comment to print a list of endnotes

\reftitle{References}

% Please provide either the correct journal abbreviation (e.g. according to the “List of Title Word Abbreviations” http://www.issn.org/services/online-services/access-to-the-ltwa/) or the full name of the journal.
% Citations and References in Supplementary files are permitted provided that they also appear in the reference list here. 

%=====================================
% References, variant A: external bibliography
%=====================================
\bibliography{reference}





% If authors have biography, please use the format below
%\section*{Short Biography of Authors}
%\bio
%{\raisebox{-0.35cm}{\includegraphics[width=3.5cm,height=5.3cm,clip,keepaspectratio]{Definitions/author1.pdf}}}
%{\textbf{Firstname Lastname} Biography of first author}
%
%\bio
%{\raisebox{-0.35cm}{\includegraphics[width=3.5cm,height=5.3cm,clip,keepaspectratio]{Definitions/author2.jpg}}}
%{\textbf{Firstname Lastname} Biography of second author}

% For the MDPI journals use author-date citation, please follow the formatting guidelines on http://www.mdpi.com/authors/references
% To cite two works by the same author: \citeauthor{ref-journal-1a} (\citeyear{ref-journal-1a}, \citeyear{ref-journal-1b}). This produces: Whittaker (1967, 1975)
% To cite two works by the same author with specific pages: \citeauthor{ref-journal-3a} (\citeyear{ref-journal-3a}, p. 328; \citeyear{ref-journal-3b}, p.475). This produces: Wong (1999, p. 328; 2000, p. 475)

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% for journal Sci
%\reviewreports{\\
%Reviewer 1 comments and authors’ response\\
%Reviewer 2 comments and authors’ response\\
%Reviewer 3 comments and authors’ response
%}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\PublishersNote{}
\end{adjustwidth}
\end{document}

