Results Section

The Results section shows that the authors tested many different machine learning models to predict NDCI, which
they use as an indicator of HABs in Campus Lake at SIU Carbondale. It's great to see that they compared 19
models and included detailed performance results (like MAE, MSE, RMSE, R?, and others) in Table 3. Using
SHAP to explain which features were most important is also a strong point, especially for environmental studies.
The spatial maps and the example from June 24, 2022, help connect the model results to real-world lake
conditions.

* Line 221: The methods section mentions the use of SHAP’s TreeExplainer, which is typically applied
to tree-based models. Could the authors clarify which specific model was used for generating the
SHAP values and feature importance plots? Given the evaluation of 19 different models, it’s important
to know whether SHAP interpretation was based on the best-performing model and why that model
was selected.

e In Figure 2b, the SHAP feature importance plot shows LST Day as the 4th most important feature.
However, in line 229, the text mentions LST Night as being important. Could the authors clarify this
inconsistency? If this is a mistake, please correct it.

e While performance metrics are reported for each model, the manuscript does not include any statistical
analysis to assess whether the differences in performance are significant. I recommend conducting
appropriate statistical tests (e.g., ANOVA, Wilcoxon signed-rank test) to compare model performance.
This would help strengthen the justification for selecting the best-performing model.

e The case study on June 24, 2022, is informative, but it is only a single instance. I recommend including
at least one contrasting case (e.g., a low-HAB or non-HAB event) to show how well the model captures
different conditions and to confirm its reliability across seasons.

Discussion section:

1. The discussion effectively highlights the practical value of the model, but validation against in-situ
chlorophyll-a or toxin data is missing, this weakens the confidence in NDCI as a standalone proxy.

2. The local recommendations are valuable but would be more actionable if they included threshold
values or clear decision rules derived from SHAP outputs.
