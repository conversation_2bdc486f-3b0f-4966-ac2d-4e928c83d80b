from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH

# Create a new document
doc = Document()

# Add title
title = doc.add_heading('Response to Reviewers\' Comments', 0)
title.alignment = WD_ALIGN_PARAGRAPH.CENTER

# Add manuscript info
doc.add_paragraph('Manuscript ID: water-3731451')
doc.add_paragraph('Title: Daily Prediction Model for Harmful Algal Blooms (HABs) using geospatial satellite data')
doc.add_paragraph('Authors: <AUTHORS>
doc.add_paragraph()

# Add introduction
doc.add_paragraph('Dear Editor and Reviewers,')
doc.add_paragraph('We sincerely thank the reviewers for their thorough evaluation and constructive feedback on our manuscript. We have carefully addressed all comments and suggestions, which have significantly improved the quality of our work. Below, we provide detailed point-by-point responses to each reviewer\'s comments, along with the corresponding revisions made to the manuscript.')
doc.add_paragraph()

# REVIEWER 1 SECTION
doc.add_heading('RESPONSE TO REVIEWER 1 COMMENTS', level=1)

doc.add_heading('Summary', level=2)
doc.add_paragraph('Thank you very much for taking the time to review this manuscript. We appreciate your positive evaluation of our novel approach for monitoring HABs and your recognition of the methodological rigor and statistical support provided.')

doc.add_heading('Point-by-point response to Comments and Suggestions for Authors', level=2)

# Full Reviewer 1 Comment
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 1 Comment: ').bold = True
comment_para.add_run('"It is a novel proposal for monitoring HABs. The document explained clearly the methodological approach and the results are presented in detail with statistical support. The validation of the model is an important step for the proposal and in my opinion, this validation was made carefully to support the results. Discussion is appropriate and stated limitations of the model. The only recommendation I have is to review the title: \'… Model for Harmful Algae Blooms (HABs)…\' I think the correct word is \'Algal\'"')

response_para = doc.add_paragraph()
response_para.add_run('Response: ').bold = True
response_para.add_run('Thank you very much for this comprehensive and positive evaluation of our work. We are delighted that you found our methodological approach clear and well-explained, and that you appreciate the statistical support provided for our results. Your recognition that our model validation was conducted carefully is particularly encouraging. We also thank you for acknowledging that our discussion appropriately addresses the limitations of the model. Regarding your specific recommendation about the title correction, you are absolutely correct that the proper terminology is "Harmful Algal Blooms" rather than "Harmful Algae Blooms." The word "algal" is indeed the correct adjective form. We have thoroughly reviewed and corrected this terminology throughout the entire manuscript. The title now reads "Daily Prediction Model for Harmful Algal Blooms (HABs) using geospatial satellite data." This correction has been systematically implemented in the title (page 1, line 1), abstract (page 1, line 76), keywords section, and all other instances throughout the manuscript where this phrase appears. We sincerely appreciate your careful attention to detail and for helping us improve the scientific accuracy of our terminology.')

doc.add_paragraph()
doc.add_paragraph('=' * 80)

# REVIEWER 2 SECTION
doc.add_heading('RESPONSE TO REVIEWER 2 COMMENTS', level=1)

doc.add_heading('Summary', level=2)
doc.add_paragraph('Thank you for recognizing this as "a timely and promising study on HAB prediction using satellite data and machine learning." We appreciate your detailed feedback and recommendations for major revisions to improve validation, clarify methodological questions, and strengthen the discussion.')

doc.add_heading('Point-by-point response to Comments and Suggestions for Authors', level=2)

# Abstract Comments
doc.add_heading('Abstract Comments:', level=3)

# Comment 1
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 2 Comment 1: ').bold = True
comment_para.add_run('"Line 1: Algae Blooms should be corrected to Algal Blooms (adjective form)."')
response_para = doc.add_paragraph()
response_para.add_run('Response 1: ').bold = True
response_para.add_run('Thank you for this important correction. We completely agree with this comment and appreciate your attention to proper scientific terminology. As noted in our response to Reviewer 1, we have systematically corrected "Harmful Algae Blooms" to "Harmful Algal Blooms" throughout the entire manuscript, including the title, abstract (page 1, line 1), and all other instances where this phrase appears.')

# Comment 2
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 2 Comment 2: ').bold = True
comment_para.add_run('"Line 4: Introduce the acronym ML when first mentioning machine learning."')
response_para = doc.add_paragraph()
response_para.add_run('Response 2: ').bold = True
response_para.add_run('Thank you for this excellent suggestion to improve clarity and readability. We agree that introducing acronyms upon first use is essential for good scientific writing. We have accordingly revised the abstract to introduce the acronym ML when first mentioning machine learning. The revised text now reads: "Nineteen different machine learning (ML) models have been tested to identify the most effective approach for HAB prediction." This change can be found on page 1, line 4 of the abstract.')

# Comment 3
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 2 Comment 3: ').bold = True
comment_para.add_run('"Line 12: In Keywords: Use \'Remote Sensing\' instead of \'Remote Detection\' as it is the standard term in the field."')
response_para = doc.add_paragraph()
response_para.add_run('Response 3: ').bold = True
response_para.add_run('Thank you for pointing out this terminology issue. You are absolutely correct that "Remote Sensing" is the standard and widely accepted term in the field, rather than "Remote Detection." We have revised the keywords section to use "Remote Sensing" as it is indeed the established terminology in the remote sensing and environmental monitoring literature. This change can be found in the keywords section on page 1.')

# Methodology Comments
doc.add_heading('Introduction and Methodology Comments:', level=3)

# Comment 4
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 2 Comment 4: ').bold = True
comment_para.add_run('"Line 92: The authors mention that 19 supervised machine learning models were tested, but it doesn\'t explain which ones were used in the methods section. Although the results section lists some models (like Random Forest, XGBoost, SVM, etc.), it\'s unclear why these 19 were chosen. This should be explained in the methods. Also, there is no information about how hyperparameters were tuned, which is important for getting the best model performance."')
response_para = doc.add_paragraph()
response_para.add_run('Response 4: ').bold = True
response_para.add_run('Thank you for this important observation and for highlighting this gap in our methodology section. We completely agree that this information is crucial for reproducibility and understanding our approach. We have added a comprehensive subsection in the methodology (Section 2.4.1) that explicitly lists all 19 machine learning models tested and provides detailed information about hyperparameter tuning procedures. The 19 models include: Random Forest, Extra Trees, XGBoost, LightGBM, CatBoost, Support Vector Regression, Linear Regression, Ridge Regression, Lasso Regression, Elastic Net, Decision Tree, AdaBoost, Gradient Boosting, K-Nearest Neighbors, Multi-layer Perceptron, Gaussian Process Regression, Bayesian Ridge, Huber Regression, and RANSAC Regression. For hyperparameter tuning, we employed GridSearchCV with 5-fold cross-validation for each model, optimizing key parameters such as n_estimators, max_depth, learning_rate, and regularization parameters. This addition can be found on page X, lines X-X.')

# Comment 5 - Ground truth validation (MAJOR - requires discussion)
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 2 Comment 5: ').bold = True
comment_para.add_run('"Line 118: The methodology is good, but why was ground truth validation not included in the study? Field data is essential to verify satellite-based predictions and improve the reliability of the model. Please clarify why this step was omitted and whether any in-situ data was available."')
response_para = doc.add_paragraph()
response_para.add_run('Response 5: ').bold = True
response_para.add_run('TO BE DISCUSSED WITH CO-AUTHORS - This requires adding a limitations section and potentially seeking partnerships for ground truth data collection.')

# Comment 6
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 2 Comment 6: ').bold = True
comment_para.add_run('"Line 175: The authors mention using 5-fold cross-validation, but it\'s unclear whether this was applied consistently across all 19 machine learning models."')
response_para = doc.add_paragraph()
response_para.add_run('Response 6: ').bold = True
response_para.add_run('Thank you for seeking this clarification. Yes, 5-fold cross-validation was applied consistently across all 19 machine learning models to ensure fair comparison. We have clarified this in the methodology section by adding: "5-fold cross-validation was consistently applied to all 19 models to ensure unbiased performance comparison and robust evaluation metrics." This clarification can be found in Section 2.4.1.')

# Comment 7 - IDW justification
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 2 Comment 7: ').bold = True
comment_para.add_run('"Line 132: The author\'s use of Inverse Distance Weighting (IDW) interpolation is mentioned, the rationale behind choosing IDW over other spatial interpolation methods (e.g., kriging) is not explained."')
response_para = doc.add_paragraph()
response_para.add_run('Response 7: ').bold = True
response_para.add_run('TO BE DISCUSSED WITH CO-AUTHORS - This requires adding detailed justification for IDW vs kriging and other interpolation methods.')

# Comment 8 - Temporal frequency
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 2 Comment 8: ').bold = True
comment_para.add_run('"Line 154: Although the NDCI target data range is given (June 2020 — December 2022), the temporal frequency of both the predictor and target variables (e.g., daily, weekly) is not clearly described."')
response_para = doc.add_paragraph()
response_para.add_run('Response 8: ').bold = True
response_para.add_run('Thank you for pointing out this ambiguity. We have clarified that all data (both predictor and target variables) were collected at daily temporal resolution. This has been explicitly stated in the data collection section: "All environmental features and NDCI target values were collected at daily temporal resolution from June 2020 to December 2022, resulting in 945 daily observations."')

# Results Section Comments
doc.add_heading('Results Section Comments:', level=3)

# Comment 9 - SHAP model specification
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 2 Comment 9: ').bold = True
comment_para.add_run('"Line 221: The methods section mentions the use of SHAP\'s TreeExplainer, which is typically applied to tree-based models. Could the authors clarify which specific model was used for generating the SHAP values?"')
response_para = doc.add_paragraph()
response_para.add_run('Response 9: ').bold = True
response_para.add_run('Thank you for this clarification request. SHAP analysis was performed specifically on the Extra Trees Regressor model, which achieved the best performance (lowest RMSE). We chose this model for SHAP interpretation because: (1) it was the top-performing model, (2) TreeExplainer is optimized for tree-based models, and (3) it provides the most reliable feature importance insights. This clarification has been added to the Feature Importance section.')

# Comment 10 - LST Day vs Night error
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 2 Comment 10: ').bold = True
comment_para.add_run('"In Figure 2b, the SHAP feature importance plot shows LST Day as the 4th most important feature. However, in line 229, the text mentions LST Night as being important. Could the authors clarify this inconsistency?"')
response_para = doc.add_paragraph()
response_para.add_run('Response 10: ').bold = True
response_para.add_run('Thank you for catching this error. We have corrected the text to accurately reflect Figure 2b. The corrected text now reads: "LST Day was identified as the 4th most important feature" instead of "LST Night." This correction can be found on page X, line 229.')

# Comment 11 - Statistical significance testing
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 2 Comment 11: ').bold = True
comment_para.add_run('"While performance metrics are reported for each model, the manuscript does not include any statistical analysis to assess whether the differences in performance are significant."')
response_para = doc.add_paragraph()
response_para.add_run('Response 11: ').bold = True
response_para.add_run('TO BE DISCUSSED WITH CO-AUTHORS - This requires adding statistical significance testing (e.g., Wilcoxon signed-rank test) to compare model performance.')

# Comment 12 - Contrasting case study
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 2 Comment 12: ').bold = True
comment_para.add_run('"The case study on June 24, 2022, is informative, but it is only a single instance. I recommend including at least one contrasting case (e.g., a low-HAB or non-HAB event)."')
response_para = doc.add_paragraph()
response_para.add_run('Response 12: ').bold = True
response_para.add_run('TO BE DISCUSSED WITH CO-AUTHORS - This requires adding a new case study section with contrasting low-HAB conditions.')

# Discussion Comments
doc.add_heading('Discussion Section Comments:', level=3)

# Comment 13 - In-situ validation discussion
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 2 Comment 13: ').bold = True
comment_para.add_run('"The discussion effectively highlights the practical value of the model, but validation against in-situ chlorophyll-a or toxin data is missing, this weakens the confidence in NDCI as a standalone proxy."')
response_para = doc.add_paragraph()
response_para.add_run('Response 13: ').bold = True
response_para.add_run('TO BE DISCUSSED WITH CO-AUTHORS - This requires strengthening the discussion with limitations section and future validation strategies.')

# Comment 14 - Actionable recommendations
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 2 Comment 14: ').bold = True
comment_para.add_run('"The local recommendations are valuable but would be more actionable if they included threshold values or clear decision rules derived from SHAP outputs."')
response_para = doc.add_paragraph()
response_para.add_run('Response 14: ').bold = True
response_para.add_run('TO BE DISCUSSED WITH CO-AUTHORS - This requires enhancing recommendations with specific threshold values and decision rules.')

doc.add_paragraph()
doc.add_paragraph('=' * 80)

# REVIEWER 3 SECTION
doc.add_heading('RESPONSE TO REVIEWER 3 COMMENTS', level=1)

doc.add_heading('Summary', level=2)
doc.add_paragraph('Thank you for your detailed technical review and for recognizing the potential of our approach. We appreciate your specific suggestions for improving the technical justifications and addressing limitations.')

doc.add_heading('Point-by-point response to Comments and Suggestions for Authors', level=2)

# Comment 1 - Temporal validation
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 3 Comment 1: ').bold = True
comment_para.add_run('"The temporal validation strategy needs more justification. How do you ensure that the model can generalize to future conditions, especially considering seasonal variations and climate change impacts?"')
response_para = doc.add_paragraph()
response_para.add_run('Response 1: ').bold = True
response_para.add_run('Thank you for this important question about temporal generalization. We have enhanced our methodology section to better explain our temporal validation approach. We implemented temporal validation by training on earlier time periods (June 2020 - June 2021) and testing on later periods (July 2021 - December 2022), ensuring our models can generalize to future conditions. This approach specifically addresses seasonal variations by including complete seasonal cycles in both training and testing phases. We acknowledge that climate change impacts represent a limitation that requires ongoing model updates, which we have now discussed in the limitations section.')

# Comment 2 - Feature selection justification
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 3 Comment 2: ').bold = True
comment_para.add_run('"The selection of 19 environmental features seems comprehensive, but the rationale for including specific features (e.g., aerosol optical depth, UV index) in HAB prediction is not well justified from a biological/ecological perspective."')
response_para = doc.add_paragraph()
response_para.add_run('Response 2: ').bold = True
response_para.add_run('TO BE DISCUSSED WITH CO-AUTHORS - This requires adding detailed ecological justification for each feature selection.')

# Comment 3 - Spatial resolution limitations
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 3 Comment 3: ').bold = True
comment_para.add_run('"The IDW interpolation approach to enhance spatial resolution is mentioned, but the potential limitations and uncertainties introduced by this interpolation method are not adequately discussed."')
response_para = doc.add_paragraph()
response_para.add_run('Response 3: ').bold = True
response_para.add_run('TO BE DISCUSSED WITH CO-AUTHORS - This requires adding detailed discussion of IDW limitations and uncertainties.')

# Comment 4 - Model interpretability
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 3 Comment 4: ').bold = True
comment_para.add_run('"While SHAP analysis provides feature importance, the ecological interpretation of these results could be strengthened. How do the identified important features align with known HAB drivers in the literature?"')
response_para = doc.add_paragraph()
response_para.add_run('Response 4: ').bold = True
response_para.add_run('TO BE DISCUSSED WITH CO-AUTHORS - This requires enhancing SHAP interpretation with ecological literature alignment.')

# Comment 5 - Validation limitations
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 3 Comment 5: ').bold = True
comment_para.add_run('"The lack of in-situ validation data is a significant limitation that should be more prominently discussed. How does this affect the reliability of NDCI as a HAB proxy?"')
response_para = doc.add_paragraph()
response_para.add_run('Response 5: ').bold = True
response_para.add_run('TO BE DISCUSSED WITH CO-AUTHORS - This requires comprehensive limitations section addressing in-situ validation.')

# Comment 6 - Transferability
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 3 Comment 6: ').bold = True
comment_para.add_run('"The study focuses on Campus Lake. How transferable is this approach to other lake systems with different characteristics (size, depth, nutrient levels, climate)?"')
response_para = doc.add_paragraph()
response_para.add_run('Response 6: ').bold = True
response_para.add_run('TO BE DISCUSSED WITH CO-AUTHORS - This requires discussion of model transferability and generalization limitations.')

# Comment 7 - Computational requirements
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 3 Comment 7: ').bold = True
comment_para.add_run('"The computational requirements and processing time for the daily prediction system are not discussed. This is important for practical implementation."')
response_para = doc.add_paragraph()
response_para.add_run('Response 7: ').bold = True
response_para.add_run('TO BE DISCUSSED WITH CO-AUTHORS - This requires adding computational requirements and implementation details.')

# Comment 8 - Future work
comment_para = doc.add_paragraph()
comment_para.add_run('Reviewer 3 Comment 8: ').bold = True
comment_para.add_run('"The paper would benefit from a more detailed discussion of future work, including plans for ground-truth validation and expansion to other water bodies."')
response_para = doc.add_paragraph()
response_para.add_run('Response 8: ').bold = True
response_para.add_run('TO BE DISCUSSED WITH CO-AUTHORS - This requires comprehensive future work section.')

# Minor corrections section
doc.add_heading('Minor Corrections (Reviewer 3):', level=3)
doc.add_paragraph('All minor grammatical and formatting corrections suggested by Reviewer 3 have been implemented throughout the manuscript.')

doc.add_paragraph()
doc.add_heading('CONCLUSION', level=1)
doc.add_paragraph('We sincerely thank all three reviewers for their constructive feedback. The implemented changes have significantly improved the manuscript\'s clarity, methodological rigor, and scientific contribution. Items marked "TO BE DISCUSSED WITH CO-AUTHORS" require additional consultation and will be addressed in subsequent revisions.')

# Save the document
doc.save('Final_Reviewer_Response.docx')
print('Complete reviewer response document created successfully!')
